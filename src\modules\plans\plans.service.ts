import { Injectable, NotFoundException, ConflictException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, In } from 'typeorm';
import { Plan, SubscriptionType } from 'src/database/entities/plan.entity';
import { UserPlan } from 'src/database/entities/user-plan.entity';
import { User, UserType } from 'src/database/entities/user.entity';
import { CreatePlanDto, UpdatePlanDto, PlanResponseDto, SubscribeToPlanDto, UserPlanResponseDto, PlanFilterDto } from 'src/database/models/plans.dto';
import { SimplifiedPlanDto } from 'src/database/models/simplified-plan.dto';
import { PagedListDto } from 'src/common/models/paged-list.dto';
import { PaginationDto } from 'src/common/models/pagination.dto';
import { ApplyPromotionToPlanDto } from 'src/database/models/apply-promotion-to-plan.dto';
import { forwardRef, Inject } from '@nestjs/common';
import { PromotionsService } from '../promotions/promotions.service';
import { Cron } from '@nestjs/schedule';
import { DiaryService } from '../diary/diary.service';
import { getCurrentUTCDate, addDaysUTC, addMonthsUTC, addYearsUTC, getStartOfDayUTC, getEndOfDayUTC } from '../../common/utils/date-utils';
import { PaymentService } from '../payment/services/payment.service';
import { InitiatePaymentDto } from '../payment/dto/payment.dto';
import { KcpPaymentMethod, PurchaseType } from '../../database/entities/payment-transaction.entity';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PlanFeature } from 'src/database/entities/plan-feature.entity';
import { TutorMatchingService } from '../tutor-matching/tutor-matching.service';
import { NotificationType } from 'src/database/entities/notification.entity';
import { NotificationHelperService } from '../notification/notification-helper.service';

// Profile paths (will be combined with FRONTEND_URL from environment)
const TUTOR_PROFILE_PATH = '/tutors/profile';
const STUDENT_PROFILE_PATH = '/dashboard/students/profile';

@Injectable()
export class PlansService {
    private readonly logger = new Logger(PlansService.name);
    private readonly frontendUrl: string;
    private readonly tutorProfileUrl: string;
    private readonly studentProfileUrl: string;

    constructor(
        @InjectRepository(Plan)
        private readonly planRepository: Repository<Plan>,
        @InjectRepository(UserPlan)
        private readonly userPlanRepository: Repository<UserPlan>,
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @InjectRepository(PlanFeature)
        private readonly planFeatureRepository: Repository<PlanFeature>,
        private readonly diaryService: DiaryService,
        private readonly jwtService: JwtService,
        private readonly configService: ConfigService,
        private readonly tutorMatchingService: TutorMatchingService,
        private readonly notificationHelper: NotificationHelperService,
        @Inject(forwardRef(() => PromotionsService))
        private readonly promotionsService: PromotionsService,
        @Inject(forwardRef(() => PaymentService))
        private readonly paymentService: PaymentService,
    ) {
        // Get frontend URL from environment variables with fallback
        this.frontendUrl = this.configService.get<string>('FRONTEND_URL') || 'http://103.209.40.213:3011';

        // Remove trailing slash if present
        if (this.frontendUrl.endsWith('/')) {
            this.frontendUrl = this.frontendUrl.slice(0, -1);
        }

        // Create full profile URLs
        this.tutorProfileUrl = `${this.frontendUrl}${TUTOR_PROFILE_PATH}`;
        this.studentProfileUrl = `${this.frontendUrl}${STUDENT_PROFILE_PATH}`;

        this.logger.log(`Frontend URL: ${this.frontendUrl}`);
        this.logger.log(`Tutor profile URL: ${this.tutorProfileUrl}`);
        this.logger.log(`Student profile URL: ${this.studentProfileUrl}`);
    }

    async findAll(filterDto?: PlanFilterDto): Promise<PagedListDto<PlanResponseDto>> {
        // Default to active plans if no filter provided
        const { status = 'active', page = 1, limit = 10, sortBy, sortDirection } = filterDto || {};

        // Build where condition based on status filter
        let whereCondition: any = {};

        if (status === 'active') {
            whereCondition.isActive = true;
        } else if (status === 'inactive') {
            whereCondition.isActive = false;
        }
        // For 'all', we don't add any isActive condition

        // Get total count for pagination
        const totalCount = await this.planRepository.count({
            where: whereCondition
        });

        // Apply pagination and filtering
        let options: any = {
            where: whereCondition,
            relations: ['planFeatures']
        };

        const skip = (page - 1) * limit;
        options.skip = skip;
        options.take = limit;

        if (sortBy && sortDirection) {
            options.order = { [sortBy]: sortDirection };
        } else {
            // Default sorting by createdAt DESC
            options.order = { createdAt: 'DESC' };
        }

        const plans = await this.planRepository.find(options);

        return new PagedListDto(
            plans.map(plan => this.toPlanResponseDto(plan)),
            totalCount
        );
    }

    async findById(id: string): Promise<PlanResponseDto> {
        const plan = await this.planRepository.findOne({
            where: { id: id },
            relations: ['planFeatures']
        });
        if (!plan) {
            throw new NotFoundException(`Plan with ID ${id} not found`);
        }
        return this.toPlanResponseDto(plan);
    }

    async create(createPlanDto: CreatePlanDto): Promise<PlanResponseDto> {
        // Check if plan with same name already exists
        const existingPlan = await this.planRepository.findOne({ where: { name: createPlanDto.name } });
        if (existingPlan) {
            throw new ConflictException(`Plan with name ${createPlanDto.name} already exists`);
        }

        // Set durationDays based on subscription type
        const durationDays = createPlanDto.subscriptionType === SubscriptionType.MONTHLY ? 30 : 365;

        const plan = this.planRepository.create({
            ...createPlanDto,
            legacyFeatures: createPlanDto.legacyFeatures || [],
            durationDays
        });

        // Handle feature IDs if provided
        if (createPlanDto.featureIds && createPlanDto.featureIds.length > 0) {
            const features = await this.planFeatureRepository.find({
                where: { id: In(createPlanDto.featureIds) }
            });

            if (features.length !== createPlanDto.featureIds.length) {
                throw new BadRequestException('One or more feature IDs are invalid');
            }

            plan.planFeatures = features;
        }

        const savedPlan = await this.planRepository.save(plan);

        // Fetch the plan with features to return
        const planWithFeatures = await this.planRepository.findOne({
            where: { id: savedPlan.id },
            relations: ['planFeatures']
        });

        return this.toPlanResponseDto(planWithFeatures);
    }

    async update(id: string, updatePlanDto: UpdatePlanDto): Promise<PlanResponseDto> {
        const plan = await this.planRepository.findOne({
            where: { id: id },
            relations: ['planFeatures']
        });
        if (!plan) {
            throw new NotFoundException(`Plan with ID ${id} not found`);
        }

        // Check if plan name is being changed and if new name already exists
        if (updatePlanDto.name && updatePlanDto.name !== plan.name) {
            const existingPlan = await this.planRepository.findOne({ where: { name: updatePlanDto.name } });
            if (existingPlan && existingPlan.id !== id) {
                throw new ConflictException(`Plan with name ${updatePlanDto.name} already exists`);
            }
        }

        // Handle feature IDs if provided
        if (updatePlanDto.featureIds && updatePlanDto.featureIds.length > 0) {
            const features = await this.planFeatureRepository.find({
                where: { id: In(updatePlanDto.featureIds) }
            });

            if (features.length !== updatePlanDto.featureIds.length) {
                throw new BadRequestException('One or more feature IDs are invalid');
            }

            plan.planFeatures = features;
        }        // If subscription type is being updated, recalculate durationDays
        if (updatePlanDto.subscriptionType) {
            updatePlanDto.durationDays = updatePlanDto.subscriptionType === SubscriptionType.MONTHLY ? 30 : 365;
        }

        // Update plan properties
        Object.assign(plan, updatePlanDto);
        const updatedPlan = await this.planRepository.save(plan);

        // Fetch the plan with features to return
        const planWithFeatures = await this.planRepository.findOne({
            where: { id: updatedPlan.id },
            relations: ['planFeatures']
        });

        return this.toPlanResponseDto(planWithFeatures);
    }

    async remove(id: string): Promise<void> {
        const plan = await this.planRepository.findOne({ where: { id: id } });
        if (!plan) {
            throw new NotFoundException(`Plan with ID ${id} not found`);
        }

        // Check if plan is being used by any user
        const userPlans = await this.userPlanRepository.find({ where: { planId: id, isActive: true } });
        if (userPlans.length > 0) {
            throw new BadRequestException(`Cannot delete plan as it is currently being used by ${userPlans.length} users`);
        }

        await this.planRepository.remove(plan);
    }

    async subscribeToPlan(subscribeToPlanDto: SubscribeToPlanDto, userId?: string, _isAdminRequest: boolean = false): Promise<UserPlanResponseDto> {
        // Use provided userId or the one from DTO
        const userIdToUse = userId || subscribeToPlanDto.userId;
        if (!userIdToUse) {
            throw new BadRequestException('User ID is required');
        }

        // Find user
        const user = await this.userRepository.findOne({ where: { id: userIdToUse } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userIdToUse} not found`);
        }

        // Check if user is a student
        if (user.type !== UserType.STUDENT) {
            throw new BadRequestException('Only students can subscribe to plans');
        }

        // Find plan with features
        const plan = await this.planRepository.findOne({
            where: { id: subscribeToPlanDto.planId },
            relations: ['planFeatures']
        });
        if (!plan) {
            throw new NotFoundException(`Plan with ID ${subscribeToPlanDto.planId} not found`);
        }

        // Check if plan is active
        if (!plan.isActive) {
            throw new BadRequestException(`Plan ${plan.name} is not active`);
        }

        // Check if user already has ANY active subscription - prevent duplicate subscriptions
        const activeUserPlans = await this.userPlanRepository.find({
            where: {
                userId: userIdToUse,
                isActive: true
            },
            relations: ['plan']
        });

        if (activeUserPlans.length > 0) {
            const activePlanNames = activeUserPlans.map(up => up.plan?.name || up.planId).join(', ');
            throw new ConflictException(`User already has active subscription(s): ${activePlanNames}. Please use the upgrade API to change plans.`);
        }

        // Calculate start and end dates in UTC
        const startDate = getCurrentUTCDate();
        let endDate: Date;

        // Set duration based on subscription type
        if (plan.subscriptionType === SubscriptionType.MONTHLY) {
            endDate = addMonthsUTC(startDate, 1); // 1 month
        } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
            endDate = addYearsUTC(startDate, 1); // 1 year
        } else {
            // Fallback to durationDays if needed
            endDate = addDaysUTC(startDate, plan.durationDays);
        }

        // Set auto-renewal based on plan default or user preference
        const autoRenew = subscribeToPlanDto.autoRenew !== undefined ?
            subscribeToPlanDto.autoRenew : plan.autoRenew;

        // Calculate next renewal date if auto-renewal is enabled
        const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

        // Check if this is a KCP payment
        const isKcpPayment = subscribeToPlanDto.paymentMethod &&
            ['kcp_card', 'kcp_bank', 'kcp_mobile'].includes(subscribeToPlanDto.paymentMethod);

        if (isKcpPayment && plan.price > 0) {
            // For KCP payments, initiate payment process
            this.logger.log(`Initiating KCP payment for plan subscription: ${plan.name}, amount: ${plan.price}`);

            // Create a temporary user plan with payment pending status
            const tempUserPlan = this.userPlanRepository.create({
                userId: userIdToUse,
                planId: subscribeToPlanDto.planId,
                startDate: startDate,
                endDate: endDate,
                isActive: false, // Will be activated after payment confirmation
                paymentReference: subscribeToPlanDto.paymentReference,
                isPaid: false, // Will be set to true after payment confirmation
                autoRenew: autoRenew,
                nextRenewalDate: nextRenewalDate,
                lastRenewalDate: startDate
            });

            const savedTempUserPlan = await this.userPlanRepository.save(tempUserPlan);

            // Prepare payment initiation request
            const paymentRequest: InitiatePaymentDto = {
                orderId: `PLAN-${savedTempUserPlan.id}-${Date.now()}`,
                amount: plan.price,
                currency: 'KRW',
                productName: `${plan.name} - ${plan.subscriptionType} subscription`,
                buyerName: user.email.split('@')[0], // Use email prefix as buyer name
                buyerEmail: user.email,
                buyerPhone: '010-0000-0000', // Default phone number - should be collected from user
                paymentMethod: this.mapPaymentMethodToKcp(subscribeToPlanDto.paymentMethod),
                purchaseType: PurchaseType.PLAN,
                referenceId: savedTempUserPlan.id,
                returnUrl: subscribeToPlanDto.returnUrl || `${this.frontendUrl}/payment/success`,
                cancelUrl: subscribeToPlanDto.cancelUrl || `${this.frontendUrl}/payment/cancel`,
                metadata: {
                    planId: plan.id,
                    userId: userIdToUse,
                    subscriptionType: plan.subscriptionType,
                    autoRenew
                }
            };

            // Initiate payment with KCP
            const paymentResponse = await this.paymentService.initiatePayment(userIdToUse, paymentRequest);

            if (paymentResponse.success) {
                // Update the temporary user plan with payment transaction ID
                savedTempUserPlan.paymentTransactionId = paymentResponse.transactionId;
                await this.userPlanRepository.save(savedTempUserPlan);

                // Generate a new token with updated plan information (but not active yet)
                const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedTempUserPlan);

                // Create response with user plan and new token
                const response = this.toUserPlanResponseDto(savedTempUserPlan, plan);
                response.access_token = newToken;
                response.paymentTransactionId = paymentResponse.transactionId;
                response.paymentUrl = paymentResponse.paymentUrl;

                return response;
            } else {
                // Payment initiation failed, remove the temporary user plan
                await this.userPlanRepository.remove(savedTempUserPlan);
                throw new BadRequestException(`Payment initiation failed: ${paymentResponse.message}`);
            }
        } else {
            // For non-KCP payments or free plans, use existing logic
            const userPlan = this.userPlanRepository.create({
                userId: userIdToUse,
                planId: subscribeToPlanDto.planId,
                startDate: startDate,
                endDate: endDate,
                isActive: true,
                paymentReference: subscribeToPlanDto.paymentReference,
                isPaid: !!subscribeToPlanDto.paymentReference || plan.price === 0, // Mark as paid if payment reference is provided or plan is free
                autoRenew: autoRenew,
                nextRenewalDate: nextRenewalDate,
                lastRenewalDate: startDate // Set initial subscription date as last renewal date
            });

            const savedUserPlan = await this.userPlanRepository.save(userPlan);

            // Create diary for student if they don't already have one
            try {
                // Only create diary for student users
            const user = await this.userRepository.findOne({ where: { id: userIdToUse } });
            if (user && user.type === UserType.STUDENT) {
                await this.diaryService.getOrCreateDiary(userIdToUse);
                this.logger.log(`Created diary for student with ID ${userIdToUse} after subscription`);

                // Ensure plan has features loaded before assigning tutors
                if (!plan.planFeatures) {
                    // Load plan with features if they weren't loaded
                    const planWithFeatures = await this.planRepository.findOne({
                        where: { id: plan.id },
                        relations: ['planFeatures']
                    });

                    if (planWithFeatures) {
                        // Assign tutors for all modules in the plan (new subscription)
                        await this.assignTutorsForPlan(userIdToUse, planWithFeatures, false);
                    } else {
                        // If we can't load the plan with features, just pass the user ID
                        await this.assignTutorsForPlan(userIdToUse, undefined, false);
                    }
                } else {
                    // Plan already has features loaded
                    await this.assignTutorsForPlan(userIdToUse, plan, false);
                }
            }
        } catch (error) {
            // Log error but don't fail the subscription process
            this.logger.error(`Failed to create diary for user ${userIdToUse}: ${error.message}`, error.stack);
        }

        // Generate a new token with updated plan information
        const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedUserPlan);

        // Create response with user plan and new token
        const response = this.toUserPlanResponseDto(savedUserPlan, plan);
        response.access_token = newToken;

        return response;
        }
    }

    /**
     * Upgrade or change user's plan
     * @param userId User ID
     * @param upgradePlanDto Upgrade plan data
     * @returns Updated user plan response
     */
    async upgradePlan(userId: string, upgradePlanDto: SubscribeToPlanDto): Promise<UserPlanResponseDto> {
        // Find user
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        const userIdToUse = user.id;

        // Find plan
        const plan = await this.planRepository.findOne({
            where: { id: upgradePlanDto.planId },
            relations: ['planFeatures']
        });

        if (!plan) {
            throw new NotFoundException(`Plan with ID ${upgradePlanDto.planId} not found`);
        }

        if (!plan.isActive) {
            throw new BadRequestException(`Plan ${plan.name} is not active`);
        }

        // Check if user has any active subscription
        const activeUserPlans = await this.userPlanRepository.find({
            where: {
                userId: userIdToUse,
                isActive: true
            },
            relations: ['plan']
        });

        if (activeUserPlans.length === 0) {
            throw new BadRequestException(`User has no active subscription to upgrade. Please use the subscribe API for new subscriptions.`);
        }

        // Check if user is trying to upgrade to the same plan
        const currentPlan = activeUserPlans[0];
        if (currentPlan.planId === upgradePlanDto.planId) {
            throw new ConflictException(`User is already subscribed to plan ${plan.name}`);
        }

        this.logger.log(`Upgrading user ${userIdToUse} from plan ${currentPlan.plan?.name || currentPlan.planId} to ${plan.name}`);

        // Store previous plan ID for tutor assignment logic
        const previousPlanId = currentPlan.planId;

        // Deactivate current active plans
        for (const activePlan of activeUserPlans) {
            // Set plan as inactive
            activePlan.isActive = false;

            // Disable auto-renewal
            activePlan.autoRenew = false;

            // Set cancellation date in UTC
            activePlan.cancellationDate = getCurrentUTCDate();

            // Add a note about the cancellation reason
            activePlan.notes = activePlan.notes || '';
            if (activePlan.notes) activePlan.notes += '\n';
            activePlan.notes += `Automatically cancelled due to plan upgrade to ${plan.name} (${plan.id}) on ${getCurrentUTCDate().toISOString()}`;

            // Save the updated plan
            await this.userPlanRepository.save(activePlan);

            this.logger.log(`Deactivated plan ${activePlan.plan?.name || activePlan.planId} for user ${userIdToUse}`);
        }

        // Calculate start and end dates in UTC
        const startDate = getCurrentUTCDate();
        let endDate: Date;

        // Set duration based on subscription type
        if (plan.subscriptionType === SubscriptionType.MONTHLY) {
            endDate = addMonthsUTC(startDate, 1); // 1 month
        } else if (plan.subscriptionType === SubscriptionType.YEARLY) {
            endDate = addYearsUTC(startDate, 1); // 1 year
        } else {
            // Fallback to durationDays if needed
            endDate = addDaysUTC(startDate, plan.durationDays);
        }

        // Set auto-renewal based on plan default or user preference
        const autoRenew = upgradePlanDto.autoRenew !== undefined ?
            upgradePlanDto.autoRenew : plan.autoRenew;

        // Calculate next renewal date if auto-renewal is enabled
        const nextRenewalDate = autoRenew ? new Date(endDate.getTime()) : undefined;

        // Check if this is a KCP payment
        const isKcpPayment = upgradePlanDto.paymentMethod &&
            ['kcp_card', 'kcp_bank', 'kcp_mobile'].includes(upgradePlanDto.paymentMethod);

        if (isKcpPayment && plan.price > 0) {
            // For KCP payments, initiate payment process
            this.logger.log(`Initiating KCP payment for plan upgrade: ${plan.name}, amount: ${plan.price}`);

            // Create a temporary user plan with payment pending status
            const tempUserPlan = this.userPlanRepository.create({
                userId: userIdToUse,
                planId: upgradePlanDto.planId,
                startDate: startDate,
                endDate: endDate,
                isActive: false, // Will be activated after payment confirmation
                paymentReference: upgradePlanDto.paymentReference,
                isPaid: false, // Will be set to true after payment confirmation
                autoRenew: autoRenew,
                nextRenewalDate: nextRenewalDate,
                lastRenewalDate: startDate
            });

            const savedTempUserPlan = await this.userPlanRepository.save(tempUserPlan);

            // Prepare payment initiation request
            const paymentRequest: InitiatePaymentDto = {
                orderId: `UPGRADE-${savedTempUserPlan.id}-${Date.now()}`,
                amount: plan.price,
                currency: 'KRW',
                productName: `${plan.name} - ${plan.subscriptionType} upgrade`,
                buyerName: user.email.split('@')[0], // Use email prefix as buyer name
                buyerEmail: user.email,
                buyerPhone: '010-0000-0000', // Default phone number - should be collected from user
                paymentMethod: this.mapPaymentMethodToKcp(upgradePlanDto.paymentMethod),
                purchaseType: PurchaseType.PLAN,
                referenceId: savedTempUserPlan.id,
                returnUrl: upgradePlanDto.returnUrl || `${this.frontendUrl}/payment/success`,
                cancelUrl: upgradePlanDto.cancelUrl || `${this.frontendUrl}/payment/cancel`,
                metadata: {
                    planId: plan.id,
                    userId: userIdToUse,
                    subscriptionType: plan.subscriptionType,
                    autoRenew,
                    isUpgrade: true,
                    previousPlanId: previousPlanId
                }
            };

            // Initiate payment with KCP
            const paymentResponse = await this.paymentService.initiatePayment(userIdToUse, paymentRequest);

            if (paymentResponse.success) {
                // Update the temporary user plan with payment transaction ID
                savedTempUserPlan.paymentTransactionId = paymentResponse.transactionId;
                await this.userPlanRepository.save(savedTempUserPlan);

                // Generate a new token with updated plan information (but not active yet)
                const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedTempUserPlan);

                // Create response with user plan and new token
                const response = this.toUserPlanResponseDto(savedTempUserPlan, plan);
                response.access_token = newToken;
                response.paymentTransactionId = paymentResponse.transactionId;
                response.paymentUrl = paymentResponse.paymentUrl;

                return response;
            } else {
                // Payment initiation failed, remove the temporary user plan and reactivate previous plan
                await this.userPlanRepository.remove(savedTempUserPlan);

                // Reactivate the previous plan
                currentPlan.isActive = true;
                currentPlan.autoRenew = true;
                currentPlan.cancellationDate = null;
                await this.userPlanRepository.save(currentPlan);

                throw new BadRequestException(`Payment initiation failed: ${paymentResponse.message}`);
            }
        } else {
            // For non-KCP payments or free plans, create the new plan immediately
            const userPlan = this.userPlanRepository.create({
                userId: userIdToUse,
                planId: upgradePlanDto.planId,
                startDate: startDate,
                endDate: endDate,
                isActive: true,
                paymentReference: upgradePlanDto.paymentReference,
                isPaid: !!upgradePlanDto.paymentReference || plan.price === 0, // Mark as paid if payment reference is provided or plan is free
                autoRenew: autoRenew,
                nextRenewalDate: nextRenewalDate,
                lastRenewalDate: startDate // Set initial subscription date as last renewal date
            });

            const savedUserPlan = await this.userPlanRepository.save(userPlan);

            // Handle tutor assignments for plan upgrade/downgrade
            try {
                // Only handle tutor assignments for student users
                if (user.type === UserType.STUDENT) {
                    await this.diaryService.getOrCreateDiary(userIdToUse);
                    this.logger.log(`Ensured diary exists for student with ID ${userIdToUse} after plan upgrade`);

                    // Assign tutors with upgrade logic
                    await this.assignTutorsForPlan(userIdToUse, plan, true, previousPlanId);
                }
            } catch (error) {
                // Log error but don't fail the upgrade process
                this.logger.error(`Failed to handle tutor assignments for user ${userIdToUse}: ${error.message}`, error.stack);
            }

            // Generate a new token with updated plan information
            const newToken = await this.generateTokenWithPlanInfo(userIdToUse, plan, savedUserPlan);

            // Create response with user plan and new token
            const response = this.toUserPlanResponseDto(savedUserPlan, plan);
            response.access_token = newToken;

            return response;
        }
    }

    async getUserPlans(userId: string, paginationDto?: PaginationDto): Promise<PagedListDto<UserPlanResponseDto>> {
        // Find user
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        // Get total count for pagination
        const totalCount = await this.userPlanRepository.count({
            where: { userId: userId }
        });

        // Apply pagination if provided
        let options: any = {
            where: { userId: userId },
            relations: ['plan', 'plan.planFeatures']
        };

        if (paginationDto) {
            const { page = 1, limit = 10, sortBy, sortDirection } = paginationDto;
            const skip = (page - 1) * limit;

            options.skip = skip;
            options.take = limit;

            if (sortBy && sortDirection) {
                options.order = { [sortBy]: sortDirection };
            } else {
                options.order = { 'createdAt': 'DESC' };
            }
        } else {
            options.order = { 'createdAt': 'DESC' };
        }

        // Find user plans with plan and plan features
        const userPlans = await this.userPlanRepository.find(options);

        return new PagedListDto(
            userPlans.map(userPlan => this.toUserPlanResponseDto(userPlan, userPlan.plan)),
            totalCount
        );
    }

    async getActiveUserPlan(userId: string): Promise<UserPlanResponseDto> {
        // Find user
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        // Find active user plan with plan and plan features
        const userPlan = await this.userPlanRepository.findOne({
            where: { userId: userId, isActive: true },
            relations: ['plan', 'plan.planFeatures']
        });

        if (!userPlan) {
            throw new NotFoundException(`User does not have an active plan`);
        }

        return this.toUserPlanResponseDto(userPlan, userPlan.plan);
    }

    async cancelUserPlan(userId: string, planId: string): Promise<void> {
        // Find user
        const user = await this.userRepository.findOne({ where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        // Find user plan
        const userPlan = await this.userPlanRepository.findOne({
            where: { userId: userId, planId: planId, isActive: true },
            relations: ['plan']
        });

        if (!userPlan) {
            throw new NotFoundException(`User does not have an active subscription to the specified plan`);
        }

        // Deactivate plan and set cancellation date
        userPlan.isActive = false;
        userPlan.autoRenew = false;
        userPlan.cancellationDate = new Date();
        await this.userPlanRepository.save(userPlan);
    }

    /**
     * Apply a promotion to multiple plans
     * @param applyPromotionToPlanDto Promotion application data
     * @returns Success message
     */
    async applyPromotionToPlans(applyPromotionToPlanDto: ApplyPromotionToPlanDto): Promise<{ success: boolean; message: string }> {
        try {
            const { promotionId, planIds } = applyPromotionToPlanDto;

            // Check if promotion exists and get its details
            let promotion: any;
            try {
                promotion = await this.promotionsService.getPromotionById(promotionId);
            } catch (error) {
                throw new NotFoundException(`Promotion with ID ${promotionId} not found`);
            }

            // Get all plans to update
            const plans = await this.planRepository.find({
                where: { id: In(planIds) }
            });

            if (plans.length === 0) {
                throw new NotFoundException(`No plans found with the provided IDs`);
            }

            // Check if any plans are missing
            if (plans.length !== planIds.length) {
                const foundPlanIds = plans.map(plan => plan.id);
                const missingPlanIds = planIds.filter(id => !foundPlanIds.includes(id));
                this.logger.warn(`Some plans were not found: ${missingPlanIds.join(', ')}`);
            }

            // Check if promotion has applicable plan restrictions
            const hasApplicablePlanRestrictions =
                promotion.applicablePlanIds &&
                promotion.applicablePlanIds.length > 0;

            // Filter plans that are in applicable plan types
            const applicablePlans = hasApplicablePlanRestrictions
                ? plans.filter(plan => promotion.applicablePlanIds.includes(plan.type))
                : plans;

            // Check if any plans are not in applicable plan types
            const nonApplicablePlans = hasApplicablePlanRestrictions
                ? plans.filter(plan => !promotion.applicablePlanIds.includes(plan.type))
                : [];

            if (hasApplicablePlanRestrictions && applicablePlans.length === 0) {
                throw new BadRequestException('None of the selected plans are applicable to this promotion');
            }

            // Apply promotion to applicable plans
            for (const plan of applicablePlans) {
                plan.isApplicableForPromotion = true;
                plan.promotionId = promotionId;
            }

            // Save updated plans
            await this.planRepository.save(applicablePlans);

            // Prepare response message based on applicable plans
            let message = `Promotion applied to ${applicablePlans.length} plans`;
            if (nonApplicablePlans.length > 0) {
                message += `. ${nonApplicablePlans.length} plans were skipped because they are not applicable to this promotion.`;
            }

            return {
                success: true,
                message: message
            };
        } catch (error) {
            this.logger.error(`Error applying promotion to plans: ${error.message}`, error.stack);

            if (error instanceof NotFoundException) {
                throw error;
            }

            throw new BadRequestException(`Failed to apply promotion to plans: ${error.message}`);
        }
    }

    /**
     * Remove promotion from a plan
     * @param planId Plan ID
     * @returns Success message
     */
    async removePromotionFromPlan(planId: string): Promise<{ success: boolean; message: string }> {
        try {
            // Find plan
            const plan = await this.planRepository.findOne({
                where: { id: planId }
            });

            if (!plan) {
                throw new NotFoundException(`Plan with ID ${planId} not found`);
            }

            // Check if plan has a promotion
            if (!plan.promotionId) {
                return {
                    success: false,
                    message: `Plan does not have an applied promotion`
                };
            }

            // Remove promotion from plan
            plan.isApplicableForPromotion = false;
            plan.promotionId = null;

            // Save updated plan
            await this.planRepository.save(plan);

            return {
                success: true,
                message: `Promotion removed from plan successfully`
            };
        } catch (error) {
            this.logger.error(`Error removing promotion from plan: ${error.message}`, error.stack);

            if (error instanceof NotFoundException) {
                throw error;
            }

            throw new BadRequestException(`Failed to remove promotion from plan: ${error.message}`);
        }
    }

    private formatLegacyFeatures(legacyFeatures: any): any[] {
        if (!legacyFeatures) {
            return [];
        }

        // If it's already an array, return it
        if (Array.isArray(legacyFeatures)) {
            return legacyFeatures;
        }

        // If it's an object with boolean values, convert it to array format
        if (typeof legacyFeatures === 'object') {
            return [
                {
                    type: 'hec_user_diary',
                    name: 'Hec User Diary',
                    description: 'Access to the HEC User Diary platform',
                    isActive: !!legacyFeatures.hecUserDiary
                },
                {
                    type: 'hec_play',
                    name: 'HEC Play',
                    description: 'Access to the HEC Play platform',
                    isActive: !!legacyFeatures.hecPlay
                },
                {
                    type: 'english_qa_writing',
                    name: 'English Q&A Writing Platform',
                    description: 'Access to the English Q&A Writing Platform',
                    isActive: !!legacyFeatures.englishQA
                },
                {
                    type: 'english_essay',
                    name: 'English Essay Platform',
                    description: 'Access to the English Essay Platform',
                    isActive: !!legacyFeatures.englishEssay
                },
                {
                    type: 'english_novel',
                    name: 'English Novel Platform',
                    description: 'Access to the English Novel Platform',
                    isActive: !!legacyFeatures.englishNovel
                }
            ];
        }

        // Default to empty array
        return [];
    }

    private toPlanResponseDto(plan: Plan): PlanResponseDto {
        return {
            id: plan.id,
            name: plan.name,
            type: plan.type,
            subscriptionType: plan.subscriptionType,
            autoRenew: plan.autoRenew,
            description: plan.description,
            price: plan.price,
            durationDays: plan.durationDays,
            legacyFeatures: this.formatLegacyFeatures(plan.legacyFeatures),
            planFeatures: plan.planFeatures ? plan.planFeatures.map(feature => ({
                id: feature.id,
                type: feature.type,
                name: feature.name,
                description: feature.description,
                createdAt: feature.createdAt,
                updatedAt: feature.updatedAt
            })) : [],
            isActive: plan.isActive,
            isApplicableForPromotion: plan.isApplicableForPromotion,
            promotionId: plan.promotionId,
            createdAt: plan.createdAt,
            updatedAt: plan.updatedAt
        };
    }

    private toUserPlanResponseDto(userPlan: UserPlan, plan: Plan): UserPlanResponseDto {
        return {
            id: userPlan.id,
            userId: userPlan.userId,
            planId: userPlan.planId,
            planName: plan.name,
            startDate: userPlan.startDate,
            endDate: userPlan.endDate,
            isActive: userPlan.isActive,
            paymentReference: userPlan.paymentReference,
            isPaid: userPlan.isPaid,
            autoRenew: userPlan.autoRenew,
            lastRenewalDate: userPlan.lastRenewalDate,
            nextRenewalDate: userPlan.nextRenewalDate,
            cancellationDate: userPlan.cancellationDate,
            notes: userPlan.notes,
            plan: this.toPlanDto(plan)
        };
    }

    /**
     * Generate a new JWT token with updated plan information
     * @param userId The user ID
     * @param plan The active plan
     * @param userPlan The user plan details
     * @returns A new JWT token
     */
    async generateTokenWithPlanInfo(userId: string, plan: Plan, userPlan: UserPlan): Promise<string> {
        // Find user with roles
        const user = await this.userRepository.findOne({
            where: { id: userId },
            relations: ['userRoles', 'userRoles.role']
        });

        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        // Get user roles
        const userRoles = user.userRoles ? user.userRoles.map(userRole => userRole.role.name) : [];

        // Create plan info for token
        const planInfo = {
            activePlan: plan.name,
            planType: plan.type,
            planId: plan.id,
            planExpiryDate: userPlan.endDate.toISOString(),
            planActive: userPlan.isActive
        };

        // Generate JWT token with plan details
        const payload = {
            id: user.id,
            username: user.userId, // Use userId instead of email
            sub: user.id,
            name: user.name,
            type: user.type,
            selectedRole: user.type, // Use the user type as selected role
            roles: userRoles,
            ...planInfo
        };

        // Get JWT secret from config
        const secret = this.configService.get<string>('JWT_SECRET');
        if (!secret) {
            this.logger.error('JWT_SECRET is not defined in environment variables');
            throw new Error('JWT_SECRET is not defined');
        }

        // Sign and return the token
        return this.jwtService.sign(payload, { secret });
    }

    /**
     * Convert a Plan entity to a SimplifiedPlanDto
     */
    private toPlanDto(plan: Plan): SimplifiedPlanDto {
        if (!plan) return null;

        return {
            id: plan.id,
            name: plan.name,
            type: plan.type,
            subscriptionType: plan.subscriptionType,
            description: plan.description,
            price: plan.price,
            features: plan.planFeatures ? plan.planFeatures.map(feature => ({
                id: feature.id,
                type: feature.type,
                name: feature.name,
                description: feature.description
            })) : [],
            isApplicableForPromotion: plan.isApplicableForPromotion,
            promotionId: plan.promotionId
        };
    }

    /**
     * Assign tutors for all modules in a plan
     * @param studentId Student ID
     * @param plan Plan with features
     */
    /**
     * Assign tutors for all modules in a student's active plan with intelligent upgrade/downgrade handling
     *
     * TUTOR ASSIGNMENT STRATEGY:
     * - NEW SUBSCRIPTION: Assign tutors for all features
     * - UPGRADE: Only assign tutors for NEW features, preserve existing tutors
     * - DOWNGRADE: Preserve ALL existing tutors (including for removed features) for future upgrades
     * - SAME FEATURES: No new assignments needed
     *
     * TUTOR SELECTION PREFERENCE:
     * - For new features, prefer different tutors than already assigned
     * - Fallback to same tutors if not enough unique tutors available
     *
     * @param studentId Student ID
     * @param plan Plan with features (optional - if not provided, will fetch the active plan)
     * @param isUpgrade Whether this is a plan change (default: false)
     * @param previousPlanId Previous plan ID for plan change scenarios
     */
    private async assignTutorsForPlan(studentId: string, plan?: Plan, isUpgrade: boolean = false, previousPlanId?: string): Promise<void> {
        try {
            this.logger.log(`Assigning tutors for student ${studentId}`);

            // Get student details for notifications
            const student = await this.userRepository.findOne({ where: { id: studentId } });
            if (!student) {
                this.logger.error(`Student with ID ${studentId} not found`);
                return;
            }

            // Get the student's active plan with features
            let userPlan: UserPlan;
            try {
                // Find active user plan with plan and plan features
                userPlan = await this.userPlanRepository.findOne({
                    where: { userId: studentId, isActive: true },
                    relations: ['plan', 'plan.planFeatures']
                });

                if (!userPlan) {
                    this.logger.log(`Student ${studentId} does not have an active plan`);
                    return;
                }

                if (!plan) {
                    // If no plan was provided, use the one from userPlan
                    plan = userPlan.plan;
                    this.logger.log(`Found active plan ${plan.name} for student ${studentId}`);
                } else {
                    this.logger.log(`Using provided plan ${plan.name} for student ${studentId}`);
                }
            } catch (error) {
                this.logger.error(`Error fetching active plan for student ${studentId}: ${error.message}`, error.stack);
                return;
            }

            // Ensure plan has features loaded
            if (!plan.planFeatures) {
                try {
                    // Load plan with features if they weren't loaded
                    plan = await this.planRepository.findOne({
                        where: { id: plan.id },
                        relations: ['planFeatures']
                    });

                    if (!plan || !plan.planFeatures) {
                        this.logger.error(`Failed to load plan features for plan ${plan?.id}`);
                        return;
                    }
                } catch (error) {
                    this.logger.error(`Error loading plan features for plan ${plan?.id}: ${error.message}`, error.stack);
                    return;
                }
            }

            // Determine which features need tutor assignment
            let featuresToAssign: PlanFeature[] = [];
            let existingAssignments: Array<{ tutor: User; module: PlanFeature; mapping: any }> = [];

            if (isUpgrade && previousPlanId) {
                // For plan changes, intelligently handle upgrades and downgrades
                this.logger.log(`Processing plan change from ${previousPlanId} to ${plan.id} for student ${studentId}`);

                // Get previous plan features
                const previousPlan = await this.planRepository.findOne({
                    where: { id: previousPlanId },
                    relations: ['planFeatures']
                });

                if (previousPlan && previousPlan.planFeatures) {
                    const previousFeatureIds = previousPlan.planFeatures.map(f => f.id);
                    const currentFeatureIds = plan.planFeatures.map(f => f.id);

                    // Find new features (in current plan but not in previous plan) - only for upgrades
                    const newFeatureIds = currentFeatureIds.filter(id => !previousFeatureIds.includes(id));

                    // Find removed features (in previous plan but not in current plan) - for downgrades
                    const removedFeatureIds = previousFeatureIds.filter(id => !currentFeatureIds.includes(id));

                    const isActualUpgrade = newFeatureIds.length > 0;
                    const isDowngrade = removedFeatureIds.length > 0;

                    if (isActualUpgrade) {
                        this.logger.log(`Plan upgrade detected: ${newFeatureIds.length} new features to assign`);
                        featuresToAssign = plan.planFeatures.filter(f => newFeatureIds.includes(f.id));
                    } else if (isDowngrade) {
                        this.logger.log(`Plan downgrade detected: ${removedFeatureIds.length} features removed, preserving all existing tutors`);
                        featuresToAssign = []; // Don't assign new tutors for downgrades
                    } else {
                        this.logger.log(`Plan change with same features, no tutor assignment needed`);
                        featuresToAssign = [];
                    }

                    this.logger.log(`Plan change: ${previousFeatureIds.length} previous features, ${currentFeatureIds.length} current features, ${newFeatureIds.length} new features, ${removedFeatureIds.length} removed features`);

                    // Get existing assignments for ALL current features (including those from removed features)
                    // This preserves tutors even for downgraded features in case of future upgrades
                    const allExistingFeatureIds = [...new Set([...previousFeatureIds, ...currentFeatureIds])];
                    for (const featureId of allExistingFeatureIds) {
                        const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, featureId);
                        if (existingAssignment) {
                            const tutor = await this.userRepository.findOne({ where: { id: existingAssignment.tutorId } });
                            // Check if this feature is in the current plan
                            const feature = plan.planFeatures.find(f => f.id === featureId);
                            if (tutor && feature) {
                                // Only include in notifications if feature is in current plan
                                existingAssignments.push({
                                    tutor,
                                    module: feature,
                                    mapping: existingAssignment
                                });
                                this.logger.log(`Preserving existing assignment: ${tutor.name} for ${feature.name}`);
                            } else if (tutor && removedFeatureIds.includes(featureId)) {
                                // Log preserved assignments for removed features (not included in notifications)
                                this.logger.log(`Preserving tutor assignment for future use: ${tutor.name} for removed feature ${featureId}`);
                            }
                        }
                    }
                } else {
                    this.logger.warn(`Could not load previous plan ${previousPlanId}, treating as new subscription`);
                    featuresToAssign = plan.planFeatures;
                }
            } else {
                // For new subscriptions, assign tutors for all features
                featuresToAssign = plan.planFeatures;
                this.logger.log(`New subscription: assigning tutors for all ${featuresToAssign.length} features`);
            }

            // CRITICAL FIX: Check for missing tutors in ALL current plan features
            // This ensures students get tutors even if previous assignments failed
            const missingTutorFeatures = [];
            for (const feature of plan.planFeatures) {
                const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, feature.id);
                if (!existingAssignment) {
                    // Check if this feature is already in featuresToAssign
                    const alreadyIncluded = featuresToAssign.some(f => f.id === feature.id);
                    if (!alreadyIncluded) {
                        missingTutorFeatures.push(feature);
                        this.logger.warn(`MISSING TUTOR DETECTED: Student ${studentId} has no tutor for feature ${feature.name} (${feature.id})`);
                    }
                }
            }

            // Add missing tutor features to assignment list
            if (missingTutorFeatures.length > 0) {
                this.logger.log(`Adding ${missingTutorFeatures.length} features with missing tutors to assignment queue`);
                featuresToAssign = [...featuresToAssign, ...missingTutorFeatures];
            }

            this.logger.log(`Found ${featuresToAssign.length} features requiring tutor assignment in plan ${plan.name} (${missingTutorFeatures.length} missing, ${featuresToAssign.length - missingTutorFeatures.length} new)`);

            // Track all tutor assignments for consolidated notification
            const tutorAssignments = [...existingAssignments];

            // Get existing tutor IDs for logging purposes
            const existingTutorIds = existingAssignments.map(assignment => assignment.tutor.id);
            this.logger.log(`Existing tutors for this student: [${existingTutorIds.join(', ')}]`);

            // Get or select the preferred tutor for this student (consistent across all features)
            let preferredTutor: User | null = null;
            try {
                preferredTutor = await this.tutorMatchingService.getOrSelectPreferredTutor(studentId);
                this.logger.log(`Selected preferred tutor ${preferredTutor.name} (${preferredTutor.id}) for student ${studentId} - will use for all features`);
            } catch (preferredTutorError) {
                this.logger.error(`Failed to get preferred tutor for student ${studentId}: ${preferredTutorError.message}`, preferredTutorError.stack);
                // Continue with individual assignment logic as fallback
            }

            // Auto-assign tutors for each new module feature
            for (const moduleFeature of featuresToAssign) {
                try {
                    if (!moduleFeature || !moduleFeature.id) {
                        this.logger.warn(`Invalid module feature found, skipping`);
                        continue;
                    }

                    const moduleId = moduleFeature.id;

                    // Check if student already has a tutor for this module (shouldn't happen for new features)
                    const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, moduleId);

                    if (existingAssignment) {
                        this.logger.log(`Student ${studentId} already has a tutor for module ${moduleId} (${moduleFeature.name}) - skipping`);
                        continue;
                    }

                    // Use consistent tutor assignment logic
                    this.logger.log(`Assigning tutor for module ${moduleId} (${moduleFeature.name}) using consistent tutor assignment`);

                    // Use the preferred tutor assignment method which ensures consistency
                    let assignments;
                    try {
                        assignments = await this.tutorMatchingService.autoAssignTutorsWithoutNotifications({
                            planFeatureId: moduleId,
                            studentIds: [studentId],
                            reassignExisting: false
                        });
                    } catch (error) {
                        this.logger.error(`Failed to assign tutor for module ${moduleId}: ${error.message}`, error.stack);
                        continue;
                    }

                    if (assignments && assignments.length > 0) {
                        // Get the tutor details
                        const assignment = assignments[0];
                        if (!assignment || !assignment.tutorId) {
                            this.logger.warn(`Invalid assignment returned for module ${moduleId}, skipping`);
                            continue;
                        }

                        const tutor = await this.userRepository.findOne({ where: { id: assignment.tutorId } });

                        if (tutor) {
                            tutorAssignments.push({
                                tutor,
                                module: moduleFeature,
                                mapping: {
                                    id: assignment.id,
                                    tutorId: assignment.tutorId,
                                    studentId: assignment.studentId,
                                    planFeatureId: assignment.planFeatureId
                                }
                            });

                            // Add this tutor to the existing tutors list to avoid assigning them again
                            existingTutorIds.push(tutor.id);
                        }

                        this.logger.log(`Successfully assigned tutor ${tutor.name} for student ${studentId} for module ${moduleId} (${moduleFeature.name})`);
                    } else {
                        this.logger.warn(`No tutors available for module ${moduleId} (${moduleFeature.name})`);
                    }
                } catch (error) {
                    // Log error but continue with other modules
                    this.logger.error(`Failed to assign tutor for student ${studentId} for module ${moduleFeature.id} (${moduleFeature.name}): ${error.message}`, error.stack);
                }
            }

            // Send consolidated notification to student if there are any assignments
            if (tutorAssignments.length > 0) {
                try {
                    this.logger.log(`Sending consolidated notification to student ${studentId} for ${tutorAssignments.length} tutor assignments`);
                    await this.sendConsolidatedTutorAssignmentNotification(student, tutorAssignments);
                    this.logger.log(`Successfully sent consolidated notification to student ${studentId}`);
                } catch (notificationError) {
                    this.logger.error(`Failed to send consolidated notification to student ${studentId}: ${notificationError.message}`, notificationError.stack);
                    // Continue with tutor notifications even if student notification fails
                }

                // Send individual notifications to tutors
                this.logger.log(`Sending individual notifications to ${tutorAssignments.length} tutors`);
                for (const assignment of tutorAssignments) {
                    try {
                        if (!assignment.tutor || !assignment.module || !assignment.mapping || !assignment.mapping.id) {
                            this.logger.warn(`Invalid assignment data, skipping tutor notification`);
                            continue;
                        }
                        this.logger.log(`Sending notification to tutor ${assignment.tutor.id} for module ${assignment.module.name}`);
                        await this.sendTutorNotification(assignment.tutor, student, assignment.module, assignment.mapping.id);
                        this.logger.log(`Successfully sent notification to tutor ${assignment.tutor.id}`);
                    } catch (tutorNotificationError) {
                        this.logger.error(`Failed to send notification to tutor ${assignment.tutor.id}: ${tutorNotificationError.message}`, tutorNotificationError.stack);
                        // Continue with other tutor notifications
                    }
                }
            }
        } catch (error) {
            this.logger.error(`CRITICAL: Failed to assign tutors for student ${studentId}: ${error.message}`, error.stack);

            // Log additional context for debugging
            this.logger.error(`Tutor assignment failure context:`, {
                studentId,
                planId: plan?.id,
                planName: plan?.name,
                isUpgrade,
                previousPlanId,
                errorType: error.constructor.name,
                errorMessage: error.message
            });

            // Don't rethrow to prevent disrupting the subscription process, but ensure visibility
        }
    }

    /**
     * Fix missing tutor assignments for a student
     * This method checks all features in the student's active plan and assigns tutors for any missing assignments
     * @param studentId Student ID to fix assignments for
     * @returns Summary of assignments created
     */
    async fixMissingTutorAssignments(studentId: string): Promise<{
        assignmentsCreated: number;
        featuresChecked: number;
        missingFeatures: string[];
        errors: string[];
    }> {
        try {
            this.logger.log(`Starting missing tutor assignment fix for student ${studentId}`);

            // Get student's active plan
            const activeUserPlan = await this.userPlanRepository.findOne({
                where: { userId: studentId, isActive: true },
                relations: ['plan', 'plan.planFeatures']
            });

            if (!activeUserPlan || !activeUserPlan.plan) {
                throw new NotFoundException(`No active plan found for student ${studentId}`);
            }

            const plan = activeUserPlan.plan;
            this.logger.log(`Found active plan: ${plan.name} with ${plan.planFeatures.length} features`);

            // Check each feature for missing tutors
            const missingFeatures = [];
            const errors = [];
            let assignmentsCreated = 0;

            for (const feature of plan.planFeatures) {
                try {
                    const existingAssignment = await this.tutorMatchingService.getStudentTutorForModule(studentId, feature.id);

                    if (!existingAssignment) {
                        missingFeatures.push(feature.name);
                        this.logger.log(`Missing tutor for feature ${feature.name}, attempting assignment`);

                        // Assign tutor for this feature using consistent tutor assignment logic
                        const assignments = await this.tutorMatchingService.autoAssignTutorsWithoutNotifications({
                            planFeatureId: feature.id,
                            studentIds: [studentId],
                            reassignExisting: false
                        });

                        if (assignments && assignments.length > 0) {
                            assignmentsCreated++;
                            this.logger.log(`Successfully assigned tutor for feature ${feature.name}`);
                        } else {
                            errors.push(`No tutors available for feature ${feature.name}`);
                            this.logger.warn(`No tutors available for feature ${feature.name}`);
                        }
                    }
                } catch (featureError) {
                    errors.push(`Failed to assign tutor for ${feature.name}: ${featureError.message}`);
                    this.logger.error(`Failed to assign tutor for feature ${feature.name}: ${featureError.message}`, featureError.stack);
                }
            }

            const result = {
                assignmentsCreated,
                featuresChecked: plan.planFeatures.length,
                missingFeatures,
                errors
            };

            this.logger.log(`Missing tutor assignment fix completed for student ${studentId}:`, result);
            return result;

        } catch (error) {
            this.logger.error(`Failed to fix missing tutor assignments for student ${studentId}: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Send a consolidated notification to a student about all their tutor assignments
     * @param student Student user
     * @param assignments Array of tutor assignments
     */
    private async sendConsolidatedTutorAssignmentNotification(
        student: User,
        assignments: Array<{ tutor: User; module: PlanFeature; mapping: { id: string } }>
    ): Promise<void> {
        try {
            if (!student || !student.id) {
                this.logger.error(`Invalid student object provided to sendConsolidatedTutorAssignmentNotification`);
                return;
            }

            if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
                this.logger.error(`No valid assignments provided for student ${student.id}`);
                return;
            }

            // Validate assignments to ensure they have all required properties
            const validAssignments = assignments.filter(assignment =>
                assignment &&
                assignment.tutor &&
                assignment.tutor.name &&
                assignment.module &&
                assignment.module.name &&
                assignment.mapping &&
                assignment.mapping.id
            );

            if (validAssignments.length === 0) {
                this.logger.error(`No valid assignments after filtering for student ${student.id}`);
                return;
            }

            // For email: Create a consolidated HTML message with tutor assignment
            // Since students now have one tutor for all features, group by tutor
            const tutorGroups = new Map<string, { tutor: any, modules: string[] }>();

            validAssignments.forEach(assignment => {
                const tutorId = assignment.tutor.id;
                if (!tutorGroups.has(tutorId)) {
                    tutorGroups.set(tutorId, {
                        tutor: assignment.tutor,
                        modules: []
                    });
                }
                tutorGroups.get(tutorId)!.modules.push(assignment.module.name);
            });

            let htmlMessage = '';

            if (tutorGroups.size === 1) {
                // Single tutor for all features
                const tutorGroup = Array.from(tutorGroups.values())[0];
                htmlMessage = `<p>You have been assigned <strong>${tutorGroup.tutor.name}</strong> as your tutor for all features.</p>
                <p>Your tutor will help you with:</p>
                <ul>`;

                tutorGroup.modules.forEach(moduleName => {
                    htmlMessage += `<li>${moduleName}</li>`;
                });

                htmlMessage += `</ul>
                <p><a href="${this.tutorProfileUrl}/${tutorGroup.tutor.id}">View Your Tutor's Profile</a></p>
                <p>Please log in to the system to interact with your tutor.</p>`;
            } else {
                // Multiple tutors (fallback for edge cases)
                htmlMessage = `<p>You have been assigned the following tutors:</p>
                <ul>`;

                tutorGroups.forEach(tutorGroup => {
                    htmlMessage += `<li><strong>${tutorGroup.tutor.name}</strong> for ${tutorGroup.modules.join(', ')} - <a href="${this.tutorProfileUrl}/${tutorGroup.tutor.id}">View Profile</a></li>`;
                });

                htmlMessage += `</ul>
                <p>Please log in to the system to interact with your tutors.</p>`;
            }

            // For in-app: Send individual notifications for each assignment
            for (const assignment of validAssignments) {
                try {
                    // Create a message for this specific assignment
                    const message = `You have been assigned ${assignment.tutor.name} as your tutor for ${assignment.module.name}.`;

                    // Send individual in-app notification
                    await this.notificationHelper.notify(
                        student.id,
                        NotificationType.TUTOR_ASSIGNMENT,
                        `New Tutor for ${assignment.module.name}`,
                        message,
                        {
                            relatedEntityId: assignment.mapping.id,
                            relatedEntityType: 'student_tutor_mapping',
                            sendInApp: true,
                            sendEmail: false,
                            sendPush: false,
                            sendMobile: false,
                            sendSms: false,
                            sendRealtime: false
                        }
                    );
                } catch (inAppError) {
                    this.logger.error(`Failed to send in-app notification for assignment to student ${student.id}: ${inAppError.message}`, inAppError.stack);
                    // Continue with other notifications
                }
            }

            try {
                // Send consolidated email notification
                // Create a text message for email (matching the HTML logic)
                let textMessage = '';

                if (tutorGroups.size === 1) {
                    // Single tutor for all features
                    const tutorGroup = Array.from(tutorGroups.values())[0];
                    textMessage = `You have been assigned ${tutorGroup.tutor.name} as your tutor for all features.\n\nYour tutor will help you with:\n`;

                    tutorGroup.modules.forEach(moduleName => {
                        textMessage += `• ${moduleName}\n`;
                    });

                    textMessage += `\nYou can view your tutor's profile in the system.`;
                } else {
                    // Multiple tutors (fallback for edge cases)
                    textMessage = `You have been assigned the following tutors:\n\n`;

                    tutorGroups.forEach(tutorGroup => {
                        textMessage += `• ${tutorGroup.tutor.name} for ${tutorGroup.modules.join(', ')}\n`;
                    });

                    textMessage += `\nYou can view your tutors' profiles in the system.`;
                }

                // Create appropriate title based on number of tutors
                const emailTitle = tutorGroups.size === 1 ? 'Your Tutor Assignment' : 'Your Tutor Assignments';

                await this.notificationHelper.notify(
                    student.id,
                    NotificationType.TUTOR_ASSIGNMENT,
                    emailTitle,
                    textMessage,
                    {
                        relatedEntityId: validAssignments[0].mapping.id, // Use the first assignment as the related entity
                        relatedEntityType: 'student_tutor_mapping',
                        htmlContent: htmlMessage,
                        sendEmail: true,
                        sendInApp: false,
                        sendPush: false,
                        sendMobile: false,
                        sendSms: false,
                        sendRealtime: false
                    }
                );
            } catch (emailError) {
                this.logger.error(`Failed to send email notification to student ${student.id}: ${emailError.message}`, emailError.stack);
                // Continue with push notification
            }

            try {
                // Send push notification for the consolidated assignments
                const pushMessage = `You have been assigned ${validAssignments.length} tutor${validAssignments.length > 1 ? 's' : ''} for your modules.`;
                await this.notificationHelper.notify(
                    student.id,
                    NotificationType.TUTOR_ASSIGNMENT,
                    'New Tutor Assignments',
                    pushMessage,
                    {
                        relatedEntityId: validAssignments[0].mapping.id,
                        relatedEntityType: 'student_tutor_mapping',
                        sendPush: true,
                        sendMobile: true,
                        sendEmail: false,
                        sendInApp: false,
                        sendSms: false,
                        sendRealtime: false
                    }
                );
            } catch (pushError) {
                this.logger.error(`Failed to send push notification to student ${student.id}: ${pushError.message}`, pushError.stack);
                // Continue execution
            }

            this.logger.log(`Sent tutor assignment notifications to student ${student.id}`);
        } catch (error) {
            this.logger.error(`Error sending notifications to student ${student.id}: ${error.message}`, error.stack);
            // Rethrow to allow the caller to handle the error
            throw error;
        }
    }

    /**
     * Send a notification to a tutor about a student assignment
     * @param tutor Tutor user
     * @param student Student user
     * @param module Module (PlanFeature)
     * @param mappingId Mapping ID
     */
    private async sendTutorNotification(
        tutor: User,
        student: User,
        module: PlanFeature,
        mappingId: string
    ): Promise<void> {
        try {
            // Validate input parameters
            if (!tutor || !tutor.id) {
                this.logger.error(`Invalid tutor object provided to sendTutorNotification`);
                return;
            }

            if (!student || !student.id || !student.name) {
                this.logger.error(`Invalid student object provided to sendTutorNotification for tutor ${tutor.id}`);
                return;
            }

            if (!module || !module.id || !module.name) {
                this.logger.error(`Invalid module object provided to sendTutorNotification for tutor ${tutor.id}`);
                return;
            }

            if (!mappingId) {
                this.logger.error(`Invalid mappingId provided to sendTutorNotification for tutor ${tutor.id}`);
                return;
            }

            // Create message for in-app notification
            const message = `You have been assigned as a tutor for ${student.name} in ${module.name}.`;

            // Create HTML message with profile link for email
            const htmlMessage = `<p>You have been assigned as a tutor for <strong>${student.name}</strong> in ${module.name}.</p>
            <p>You can view the student's profile <a href="${this.studentProfileUrl}/${student.id}">here</a>.</p>
            <p>Please log in to the system to interact with your student.</p>
            <p>As a tutor, you will be responsible for:</p>
            <ul>
                <li>Reviewing the student's diary entries</li>
                <li>Providing feedback and corrections</li>
                <li>Answering any questions the student may have</li>
            </ul>
            <p>Thank you for your dedication to helping our students improve their skills!</p>`;

            // Send in-app notification
            try {
                await this.notificationHelper.notify(
                    tutor.id,
                    NotificationType.TUTOR_ASSIGNMENT,
                    `New Student for ${module.name}`,
                    message,
                    {
                        relatedEntityId: mappingId,
                        relatedEntityType: 'student_tutor_mapping',
                        sendInApp: true,
                        sendEmail: false,
                        sendPush: false,
                        sendMobile: false,
                        sendSms: false,
                        sendRealtime: false
                    }
                );
            } catch (inAppError) {
                this.logger.error(`Failed to send in-app notification to tutor ${tutor.id}: ${inAppError.message}`, inAppError.stack);
                // Continue with other notifications
            }

            // Send email notification with more details
            try {
                await this.notificationHelper.notify(
                    tutor.id,
                    NotificationType.TUTOR_ASSIGNMENT,
                    `New Student Assignment: ${student.name} for ${module.name}`,
                    message,
                    {
                        relatedEntityId: mappingId,
                        relatedEntityType: 'student_tutor_mapping',
                        htmlContent: htmlMessage,
                        sendEmail: true,
                        sendInApp: false,
                        sendPush: false,
                        sendMobile: false,
                        sendSms: false,
                        sendRealtime: false
                    }
                );
            } catch (emailError) {
                this.logger.error(`Failed to send email notification to tutor ${tutor.id}: ${emailError.message}`, emailError.stack);
                // Continue with push notification
            }

            // Send push notification
            try {
                const pushMessage = `New student ${student.name} assigned for ${module.name}`;
                await this.notificationHelper.notify(
                    tutor.id,
                    NotificationType.TUTOR_ASSIGNMENT,
                    `New Student Assignment`,
                    pushMessage,
                    {
                        relatedEntityId: mappingId,
                        relatedEntityType: 'student_tutor_mapping',
                        sendPush: true,
                        sendMobile: true,
                        sendEmail: false,
                        sendInApp: false,
                        sendSms: false,
                        sendRealtime: false
                    }
                );
            } catch (pushError) {
                this.logger.error(`Failed to send push notification to tutor ${tutor.id}: ${pushError.message}`, pushError.stack);
                // Continue execution
            }

            this.logger.log(`Sent tutor assignment notifications to tutor ${tutor.id} for student ${student.id} in module ${module.id} (${module.name})`);
        } catch (error) {
            this.logger.error(`Error sending notifications to tutor ${tutor.id}: ${error.message}`, error.stack);
            // Rethrow to allow the caller to handle the error
            throw error;
        }
    }

    // Cron job to handle auto-renewals (runs daily at midnight)
    @Cron('0 0 * * *')
    async handleAutoRenewals() {
        try {
            // Get today's date in UTC
            const today = getCurrentUTCDate();
            const startOfDay = getStartOfDayUTC(today);
            const endOfDay = getEndOfDayUTC(today);

            this.logger.log(`Checking for subscriptions due for renewal between ${startOfDay.toISOString()} and ${endOfDay.toISOString()}`);

            // Find all active subscriptions that are due for renewal
            let subscriptionsDueForRenewal: UserPlan[] = [];
            try {
                subscriptionsDueForRenewal = await this.userPlanRepository.find({
                    where: {
                        isActive: true,
                        autoRenew: true,
                        nextRenewalDate: Between(startOfDay, endOfDay)
                    },
                    relations: ['plan']
                });
            } catch (dbError) {
                this.logger.error(`Database error when fetching subscriptions due for renewal: ${dbError.message}`, dbError.stack);
                return;
            }

            if (!subscriptionsDueForRenewal || !Array.isArray(subscriptionsDueForRenewal)) {
                this.logger.error(`Invalid result when fetching subscriptions due for renewal`);
                return;
            }

            this.logger.log(`Found ${subscriptionsDueForRenewal.length} subscriptions due for renewal`);

            for (const subscription of subscriptionsDueForRenewal) {
                try {
                    // Validate subscription data
                    if (!subscription || !subscription.id || !subscription.userId || !subscription.planId) {
                        this.logger.error(`Invalid subscription data, skipping renewal`);
                        continue;
                    }

                    if (!subscription.plan) {
                        this.logger.error(`Subscription ${subscription.id} has no associated plan, skipping renewal`);
                        continue;
                    }

                    // Process renewal (in a real system, this would handle payment processing)
                    this.logger.log(`Processing renewal for subscription ${subscription.id} (User: ${subscription.userId}, Plan: ${subscription.planId})`);

                    // Calculate new end date based on subscription type
                    let newEndDate: Date;

                    if (subscription.plan.subscriptionType === SubscriptionType.MONTHLY) {
                        newEndDate = addMonthsUTC(subscription.endDate, 1);
                    } else if (subscription.plan.subscriptionType === SubscriptionType.YEARLY) {
                        newEndDate = addYearsUTC(subscription.endDate, 1);
                    } else {
                        newEndDate = addDaysUTC(subscription.endDate, subscription.plan.durationDays);
                    }

                    // Update subscription
                    subscription.lastRenewalDate = getCurrentUTCDate();
                    subscription.endDate = newEndDate;
                    subscription.nextRenewalDate = new Date(newEndDate.getTime());

                    try {
                        await this.userPlanRepository.save(subscription);
                        this.logger.log(`Successfully renewed subscription ${subscription.id} until ${newEndDate.toISOString()}`);
                    } catch (saveError) {
                        this.logger.error(`Failed to save renewed subscription ${subscription.id}: ${saveError.message}`, saveError.stack);
                        continue;
                    }

                    // Check if user is a student and assign tutors if needed
                    try {
                        const user = await this.userRepository.findOne({ where: { id: subscription.userId } });
                        if (!user) {
                            this.logger.warn(`User ${subscription.userId} not found for subscription ${subscription.id}, skipping tutor assignment`);
                            continue;
                        }

                        if (user.type === UserType.STUDENT) {
                            // Get the plan with features
                            const plan = await this.planRepository.findOne({
                                where: { id: subscription.planId },
                                relations: ['planFeatures']
                            });

                            if (!plan) {
                                this.logger.warn(`Plan ${subscription.planId} not found for subscription ${subscription.id}, skipping tutor assignment`);
                                continue;
                            }

                            // Ensure plan has features loaded before assigning tutors
                            if (!plan.planFeatures) {
                                // Load plan with features if they weren't loaded
                                const planWithFeatures = await this.planRepository.findOne({
                                    where: { id: plan.id },
                                    relations: ['planFeatures']
                                });

                                if (planWithFeatures) {
                                    // Assign tutors for the renewed plan
                                    await this.assignTutorsForPlan(subscription.userId, planWithFeatures);
                                } else {
                                    // If we can't load the plan with features, just pass the user ID
                                    await this.assignTutorsForPlan(subscription.userId);
                                }
                            } else {
                                // Plan already has features loaded
                                await this.assignTutorsForPlan(subscription.userId, plan);
                            }
                        }
                    } catch (tutorError) {
                        // Log error but don't fail the renewal process
                        this.logger.error(`Failed to assign tutors after renewal for user ${subscription.userId}: ${tutorError.message}`, tutorError.stack);
                    }
                } catch (error) {
                    // Log error and continue with next subscription
                    this.logger.error(`Failed to renew subscription ${subscription?.id || 'unknown'}: ${error.message}`, error.stack);
                }
            }
        } catch (error) {
            this.logger.error(`Unhandled error in handleAutoRenewals: ${error.message}`, error.stack);
        }
    }

    /**
     * Map payment method string to KCP payment method enum
     */
    private mapPaymentMethodToKcp(paymentMethod: string): KcpPaymentMethod {
        switch (paymentMethod) {
            case 'kcp_card':
                return KcpPaymentMethod.CARD;
            case 'kcp_bank':
                return KcpPaymentMethod.BANK;
            case 'kcp_mobile':
                return KcpPaymentMethod.MOBILE;
            default:
                return KcpPaymentMethod.CARD;
        }
    }
}
