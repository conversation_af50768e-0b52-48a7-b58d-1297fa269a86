import { DataSource } from 'typeorm';
import { TestDataSeeder } from './test-data.seed';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { User, UserType } from '../../database/entities/user.entity';

// Import the existing database configuration
import { AppDataSource } from '../database.config';

async function runTestSeeder() {
  try {
    console.log('🚀 Starting simple test data seeder...');

    // Initialize database connection
    await AppDataSource.initialize();
    console.log('✅ Database connection established');

    // Step 1: Create test students
    console.log('\n📊 Step 1: Creating test students...');
    const testSeeder = new TestDataSeeder(AppDataSource);
    await testSeeder.run();

    // Step 2: Run award calculations
    console.log('\n🏆 Step 2: Running award calculations...');

    // Get all awards
    const awardRepository = AppDataSource.getRepository(Award);
    const awards = await awardRepository.find();
    console.log(`   Found ${awards.length} awards configured`);

    // Calculate awards for each module and frequency
    const modules = ['diary', 'novel', 'essay'];
    const frequencies = ['weekly', 'monthly', 'yearly'];

    for (const module of modules) {
      for (const frequency of frequencies) {
        const moduleAwards = awards.filter(a => a.module === module && a.frequency === frequency);
        console.log(`   📈 ${module} - ${frequency}: ${moduleAwards.length} awards`);

        // For now, just log what would be calculated
        // In a real implementation, this would call the award calculation service
        for (const award of moduleAwards) {
          console.log(`      - ${award.name} (${award.rewardPoints} points)`);
        }
      }
    }

    // Step 3: Show summary
    console.log('\n📊 Summary:');
    const userRepository = AppDataSource.getRepository(User);
    const awardWinnerRepository = AppDataSource.getRepository(AwardWinner);

    const studentCount = await userRepository.count({ where: { type: UserType.STUDENT } });
    const awardWinnerCount = await awardWinnerRepository.count();

    console.log(`   👥 Students: ${studentCount}`);
    console.log(`   🏆 Awards configured: ${awards.length}`);
    console.log(`   🎖️  Award winners: ${awardWinnerCount}`);

    console.log('\n✅ Test seeder completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('   1. Test Hall of Fame API endpoints');
    console.log('   2. Run award scheduler manually');
    console.log('   3. Verify award calculation logic');

  } catch (error) {
    console.error('❌ Error running test seeder:', error);
  } finally {
    // Close database connection
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the seeder
runTestSeeder();
