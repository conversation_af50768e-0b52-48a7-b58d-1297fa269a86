import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsNumber, IsPositive, IsOptional, IsBoolean, IsEnum, IsIn } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { PaginationDto } from '../../../common/models/pagination.dto';

/**
 * DTO for querying story makers
 */
export class GetStoriesQueryDto extends PaginationDto {
  @ApiProperty({
    description: 'Search term for story title',
    example: 'Adventure',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'Filter by active status',
    example: true,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['title', 'createdAt', 'score'],
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['title', 'createdAt', 'score'])
  override sortBy?: string = 'createdAt';
}

/**
 * DTO for creating a new story maker
 */
export class CreateStoryMakerDto {
  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  @IsString()
  @IsNotEmpty()
  instruction: string;

  // Note: picture is handled separately as a file upload

  @ApiProperty({
    description: 'The total score for the story',
    example: 50,
  })
  @IsNumber()
  @IsPositive()
  score: number;

  @ApiProperty({
    description: 'The word limit for the story (optional)',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  word_limit?: number;

  @ApiProperty({
    description: 'The deadline in days for the story (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  deadline?: number;
}

/**
 * DTO for updating a story maker
 */
export class UpdateStoryMakerDto {
  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Enchanted Forest',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure with fairies</p>',
    required: false,
  })
  @IsString()
  @IsOptional()
  instruction?: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/enchanted-forest.jpg',
    required: false,
  })
  @IsOptional()
  @IsString()
  picture?: string;

  @ApiProperty({
    description: 'The total score for the story',
    example: 60,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  score?: number;

  @ApiProperty({
    description: 'Whether the story is active and available to students',
    example: true,
    required: false,
  })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'The word limit for the story (optional)',
    example: 500,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  word_limit?: number;

  @ApiProperty({
    description: 'The deadline in days for the story (optional)',
    example: 2,
    required: false,
  })
  @IsNumber()
  @IsPositive()
  @IsOptional()
  deadline?: number;
}

/**
 * DTO for story maker response
 */
export class StoryMakerResponseDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiProperty({
    description: 'The total score for the story',
    example: 50,
  })
  score: number;

  @ApiProperty({
    description: 'Whether the story is active and available to students',
    example: true,
  })
  is_active: boolean;

  @ApiProperty({
    description: 'The word limit for the story (if set)',
    example: 500,
    required: false,
  })
  word_limit?: number;

  @ApiProperty({
    description: 'The deadline in days for the story (if set)',
    example: 2,
    required: false,
  })
  deadline?: number;

  @ApiProperty({
    description: 'When the story was created',
    example: '2023-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: 'When the story was last updated',
    example: '2023-01-02T00:00:00.000Z',
  })
  updated_at: Date;
}

/**
 * DTO for toggling story active status
 */
export class ToggleStoryStatusDto {
  @ApiProperty({
    description: 'Whether the story should be active',
    example: true,
  })
  @Transform(({ value }) => {
    if (value === undefined || value === null || value === '') return undefined;
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean()
  is_active: boolean;
}

/**
 * DTO for evaluation details in game response
 */
export class EvaluationDetailDto {
  @ApiProperty({
    description: 'The ID of the evaluation',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The corrections provided by the tutor',
    example: '<p>Here are some corrections to your story...</p>',
  })
  corrections: string;

  @ApiProperty({
    description: 'The feedback provided by the tutor',
    example: 'Great job on your creative descriptions!',
    required: false,
  })
  feedback?: string;

  @ApiProperty({
    description: 'When the submission was evaluated',
    example: '2023-01-02T00:00:00.000Z',
  })
  evaluated_at: Date;

  @ApiProperty({
    description: 'The ID of the tutor who evaluated the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  tutor_id: string;

  @ApiProperty({
    description: 'The name of the tutor who evaluated the submission',
    example: 'John Doe',
    required: false,
  })
  tutor_name?: string;

  @ApiProperty({
    description: 'The profile picture of the tutor who evaluated the submission',
    example: 'https://example.com/images/profile.jpg',
    required: false,
  })
  tutor_profile_picture?: string;
}

/**
 * DTO for submission details in game response
 */
export class SubmissionDetailDto {
  @ApiProperty({
    description: 'The ID of the submission',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The content of the submission in rich text format',
    example: '<p>Once upon a time in a magical forest...</p>',
  })
  content: string;

  @ApiProperty({
    description: 'When the submission was submitted',
    example: '2023-01-01T00:00:00.000Z',
  })
  submitted_at: Date;

  @ApiProperty({
    description: 'Whether the submission has been evaluated',
    example: true,
  })
  is_evaluated: boolean;

  @ApiProperty({
    description: 'The evaluation details if the submission has been evaluated',
    type: EvaluationDetailDto,
    required: false,
  })
  evaluation?: EvaluationDetailDto;
}

/**
 * DTO for story maker game details (student view)
 */
export class StoryMakerGameDetailDto {
  @ApiProperty({
    description: 'The ID of the story',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'The title of the story',
    example: 'Adventure in the Forest',
  })
  title: string;

  @ApiProperty({
    description: 'The instruction text in rich text format',
    example: '<p>Create a story about a magical forest adventure</p>',
  })
  instruction: string;

  @ApiProperty({
    description: 'URL or path to the picture for the story',
    example: 'https://example.com/images/forest.jpg',
  })
  picture: string;

  @ApiProperty({
    description: 'The total score for the story',
    example: 50,
  })
  score: number;

  @ApiProperty({
    description: 'The word limit for the story (if set)',
    example: 500,
    required: false,
  })
  word_limit?: number;

  @ApiProperty({
    description: 'The deadline in days for the story (if set)',
    example: 2,
    required: false,
  })
  deadline?: number;

  @ApiProperty({
    description: 'Whether the student has played this game',
    example: true,
  })
  is_played: boolean;

  @ApiProperty({
    description: 'The score the student received for this story (if played and evaluated)',
    example: 45,
    required: false,
  })
  participation_score?: number;

  @ApiProperty({
    description: 'The latest submission details (if played)',
    type: SubmissionDetailDto,
    required: false,
  })
  latest_submission?: SubmissionDetailDto;
}
