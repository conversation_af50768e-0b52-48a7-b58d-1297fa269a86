import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON>, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { MissionDiaryEntry } from './mission-diary-entry.entity';

@Entity()
@Index(['missionEntryId', 'versionNumber'])
@Index(['missionEntryId', 'isLatest'])
@Index(['createdAt'])
export class MissionDiaryEntryHistory extends AuditableBaseEntity {
  @Column({ name: 'mission_entry_id', type: 'uuid' })
  missionEntryId: string;

  @ManyToOne(() => MissionDiaryEntry, entry => entry.versions)
  @JoinColumn({ name: 'mission_entry_id' })
  missionEntry: MissionDiaryEntry;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'version_number', type: 'integer' })
  versionNumber: number;

  @Column({ name: 'is_latest', type: 'boolean', default: false })
  isLatest: boolean;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true
  })
  metaData: any;
}
