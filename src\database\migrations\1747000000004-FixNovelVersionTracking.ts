import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixNovelVersionTracking1747000000004 implements MigrationInterface {
  name = 'FixNovelVersionTracking1747000000004';

  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Fixing novel version tracking...');

    // 1. Fix version numbers - ensure they are sequential
    const entriesWithHistory = await queryRunner.query(`
      SELECT DISTINCT novel_entry_id 
      FROM novel_entry_history
    `);

    console.log(`Found ${entriesWithHistory.length} entries with history to fix`);

    for (const entryData of entriesWithHistory) {
      const entryId = entryData.novel_entry_id;
      
      try {
        // Get all versions for this entry ordered by creation date
        const versions = await queryRunner.query(`
          SELECT id, created_at
          FROM novel_entry_history 
          WHERE novel_entry_id = $1
          ORDER BY created_at ASC
        `, [entryId]);

        // Update version numbers to be sequential
        for (let i = 0; i < versions.length; i++) {
          const versionNumber = i + 1;
          const isLatest = i === versions.length - 1;
          
          await queryRunner.query(`
            UPDATE novel_entry_history 
            SET 
              version_number = $1,
              is_latest = $2
            WHERE id = $3
          `, [versionNumber, isLatest, versions[i].id]);
        }

        // Update the novel entry with correct totals and current version
        const latestVersionId = versions[versions.length - 1]?.id;
        
        await queryRunner.query(`
          UPDATE novel_entry 
          SET 
            total_versions = $1,
            current_version_id = $2
          WHERE id = $3
        `, [versions.length, latestVersionId, entryId]);

        console.log(`Fixed entry ${entryId}: ${versions.length} versions`);

      } catch (error) {
        console.error(`Error fixing entry ${entryId}:`, error.message);
      }
    }

    // 2. Ensure entries without history have correct defaults
    await queryRunner.query(`
      UPDATE novel_entry 
      SET 
        total_versions = 0,
        current_version_id = NULL
      WHERE id NOT IN (
        SELECT DISTINCT novel_entry_id 
        FROM novel_entry_history
      )
      AND (total_versions != 0 OR current_version_id IS NOT NULL)
    `);

    console.log('Novel version tracking fix completed');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Rolling back novel version tracking fix...');
    
    // Reset all version numbers to 1 (original state)
    await queryRunner.query(`
      UPDATE novel_entry_history 
      SET version_number = 1, is_latest = true
    `);

    console.log('Rollback completed');
  }
}
