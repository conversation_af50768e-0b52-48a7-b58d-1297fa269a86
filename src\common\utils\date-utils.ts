/**
 * Date utility functions for consistent date handling across the application.
 * All dates should be stored and processed in UTC time for consistency.
 */

/**
 * Creates a new Date object in UTC
 * @param year The year
 * @param month The month (0-11)
 * @param day The day of the month
 * @param hours The hour (0-23)
 * @param minutes The minutes (0-59)
 * @param seconds The seconds (0-59)
 * @param ms The milliseconds (0-999)
 * @returns A new Date object in UTC
 */
export function createUTCDate(
  year: number,
  month: number,
  day: number,
  hours: number = 0,
  minutes: number = 0,
  seconds: number = 0,
  ms: number = 0
): Date {
  return new Date(Date.UTC(year, month, day, hours, minutes, seconds, ms));
}

/**
 * Converts a local date to UTC
 * @param date The local date to convert
 * @returns The date in UTC
 */
export function toUTCDate(date: Date): Date {
  return new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds(),
    date.getMilliseconds()
  ));
}

/**
 * Gets the current date and time in UTC
 * @returns The current date and time in UTC
 */
export function getCurrentUTCDate(): Date {
  const now = new Date();
  return toUTCDate(now);
}

/**
 * Gets the start of the day (00:00:00.000) in UTC for a given date
 * @param date The date to get the start of the day for
 * @returns The start of the day in UTC
 */
export function getStartOfDayUTC(date: Date): Date {
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    0, 0, 0, 0
  ));
}

/**
 * Gets the end of the day (23:59:59.999) in UTC for a given date
 * @param date The date to get the end of the day for
 * @returns The end of the day in UTC
 */
export function getEndOfDayUTC(date: Date): Date {
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    date.getUTCDate(),
    23, 59, 59, 999
  ));
}

/**
 * Parses a date string in YYYY-MM-DD format to a UTC Date object
 * @param dateString The date string in YYYY-MM-DD format
 * @returns A Date object in UTC, or null if the date string is invalid
 */
export function parseYYYYMMDDToUTC(dateString: string): Date | null {
  if (!dateString) return null;

  // Check if the string is in YYYY-MM-DD format
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) {
    return null;
  }

  // Parse the date string (YYYY-MM-DD)
  const [year, month, day] = dateString.split('-').map(Number);

  // Create a UTC date (month is 0-indexed in JavaScript Date)
  const date = createUTCDate(year, month - 1, day);

  // Check if date is valid
  if (isNaN(date.getTime())) {
    return null;
  }

  return date;
}

/**
 * Formats a Date object to YYYY-MM-DD string
 * @param date The Date object to format
 * @returns Formatted date string in YYYY-MM-DD format, or null if the date is invalid
 */
export function formatToYYYYMMDD(date: Date | string): string | null {
  if (!date) return null;

  try {
    // If date is a string, try to convert it to a Date object
    let dateObj: Date;
    if (typeof date === 'string') {
      // Check if the string is already in YYYY-MM-DD format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(date)) {
        return date; // Already in the correct format
      }

      // Try to parse the string to a Date object
      dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) {
        return null; // Invalid date string
      }
    } else {
      dateObj = date;
      if (isNaN(dateObj.getTime())) {
        return null; // Invalid Date object
      }
    }

    const year = dateObj.getUTCFullYear();
    // Month is 0-indexed in JavaScript, so add 1 and pad with leading zero if needed
    const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
    // Pad day with leading zero if needed
    const day = String(dateObj.getUTCDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('Error formatting date to YYYY-MM-DD:', error);
    return null;
  }
}

/**
 * Adds a specified number of days to a date
 * @param date The date to add days to
 * @param days The number of days to add
 * @returns A new Date with the days added
 */
export function addDaysUTC(date: Date, days: number): Date {
  const result = new Date(date.getTime());
  result.setUTCDate(result.getUTCDate() + days);
  return result;
}

/**
 * Adds a specified number of minutes to a date
 * @param date The date to add minutes to
 * @param minutes The number of minutes to add
 * @returns A new Date with the minutes added
 */
export function addMinutesUTC(date: Date, minutes: number): Date {
  const result = new Date(date.getTime());
  result.setUTCMinutes(result.getUTCMinutes() + minutes);
  return result;
}

/**
 * Adds a specified number of hours to a date
 * @param date The date to add hours to
 * @param hours The number of hours to add
 * @returns A new Date with the hours added
 */
export function addHoursUTC(date: Date, hours: number): Date {
  const result = new Date(date.getTime());
  result.setUTCHours(result.getUTCHours() + hours);
  return result;
}

/**
 * Adds a specified number of months to a date
 * @param date The date to add months to
 * @param months The number of months to add
 * @returns A new Date with the months added
 */
export function addMonthsUTC(date: Date, months: number): Date {
  const result = new Date(date.getTime());
  result.setUTCMonth(result.getUTCMonth() + months);
  return result;
}

/**
 * Adds a specified number of years to a date
 * @param date The date to add years to
 * @param years The number of years to add
 * @returns A new Date with the years added
 */
export function addYearsUTC(date: Date, years: number): Date {
  const result = new Date(date.getTime());
  result.setUTCFullYear(result.getUTCFullYear() + years);
  return result;
}

/**
 * Gets the start of the month (first day at 00:00:00.000) in UTC for a given date
 * @param date The date to get the start of the month for
 * @returns The start of the month in UTC
 */
export function getStartOfMonthUTC(date: Date): Date {
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth(),
    1,
    0, 0, 0, 0
  ));
}

/**
 * Gets the end of the month (last day at 23:59:59.999) in UTC for a given date
 * @param date The date to get the end of the month for
 * @returns The end of the month in UTC
 */
export function getEndOfMonthUTC(date: Date): Date {
  return new Date(Date.UTC(
    date.getUTCFullYear(),
    date.getUTCMonth() + 1,
    0,
    23, 59, 59, 999
  ));
}

/**
 * Gets the start of the week (Monday at 00:00:00.000) in UTC for a given date
 * @param date The date to get the start of the week for
 * @returns The start of the week in UTC
 */
export function getStartOfWeekUTC(date: Date): Date {
  const dayOfWeek = date.getUTCDay();
  const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Monday is start of week
  const startOfWeek = new Date(date.getTime());
  startOfWeek.setUTCDate(date.getUTCDate() - daysToSubtract);
  startOfWeek.setUTCHours(0, 0, 0, 0);
  return startOfWeek;
}

/**
 * Gets the end of the week (Sunday at 23:59:59.999) in UTC for a given date
 * @param date The date to get the end of the week for
 * @returns The end of the week in UTC
 */
export function getEndOfWeekUTC(date: Date): Date {
  const dayOfWeek = date.getUTCDay();
  const daysToAdd = dayOfWeek === 0 ? 0 : 7 - dayOfWeek; // Sunday is end of week
  const endOfWeek = new Date(date.getTime());
  endOfWeek.setUTCDate(date.getUTCDate() + daysToAdd);
  endOfWeek.setUTCHours(23, 59, 59, 999);
  return endOfWeek;
}

/**
 * Calculates age based on date of birth
 * @param dateOfBirth Date of birth in YYYY-MM-DD format or Date object
 * @returns Age in years, or null if the date is invalid
 */
export function calculateAge(dateOfBirth: Date | string): number | null {
  if (!dateOfBirth) {
    return null;
  }

  let dob: Date;

  try {
    if (typeof dateOfBirth === 'string') {
      // Check if the string is in YYYY-MM-DD format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (dateRegex.test(dateOfBirth)) {
        dob = parseYYYYMMDDToUTC(dateOfBirth);
        if (!dob) return null;
      } else {
        // Try to parse as a regular date string
        dob = new Date(dateOfBirth);
        if (isNaN(dob.getTime())) {
          console.warn(`Invalid date string format for calculateAge: ${dateOfBirth}`);
          return null;
        }
      }
    } else {
      // It's a Date object
      dob = dateOfBirth;
      if (isNaN(dob.getTime())) {
        console.warn('Invalid Date object for calculateAge');
        return null;
      }
    }

    const today = new Date();
    let age = today.getUTCFullYear() - dob.getUTCFullYear();
    const monthDiff = today.getUTCMonth() - dob.getUTCMonth();

    // If birthday hasn't occurred yet this year, subtract 1 from age
    if (monthDiff < 0 || (monthDiff === 0 && today.getUTCDate() < dob.getUTCDate())) {
      age--;
    }

    return age;
  } catch (error) {
    console.error('Error calculating age:', error);
    return null;
  }
}
