import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsUUI<PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { MissionEntryStatus } from '../entities/mission-diary-entry.entity';
import { DiaryMissionResponseDto } from './diary-mission.dto';
import { DiarySkinResponseDto } from './diary.dto';

// Request DTOs

/**
 * DTO for creating a new mission diary entry
 */
export class CreateMissionDiaryEntryDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the mission' })
  @IsNotEmpty()
  @IsUUID()
  missionId: string;

  @ApiProperty({ example: 'My favorite book is...', description: 'Content of the diary entry' })
  @IsNotEmpty()
  @IsString()
  content: string;
}

/**
 * DTO for updating a mission diary entry
 */
export class UpdateMissionDiaryEntryDto {
  @ApiProperty({ example: 'Updated content about my favorite book...', description: 'Content of the diary entry', required: false })
  @IsOptional()
  @IsString()
  content?: string;
}

/**
 * DTO for submitting a mission diary entry
 * This DTO is intentionally empty as submission is a state change operation
 * that doesn't require additional data beyond the entry ID, which is provided in the URL path parameter.
 */
export class SubmitMissionDiaryEntryDto {
  // No fields needed - the entry ID is provided in the URL path parameter
}

/**
 * DTO for adding feedback to a mission diary entry
 */
export class AddMissionFeedbackDto {
  @ApiProperty({ example: 'Great job on your analysis of the book!', description: 'Feedback text' })
  @IsNotEmpty()
  @IsString()
  feedback: string;

  @ApiProperty({ example: 4, description: 'Rating (1-5)', required: false })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(5)
  rating?: number;
}

/**
 * DTO for adding correction to a mission diary entry
 * @deprecated Use AddMissionCorrectionWithScoreDto instead
 */
export class AddMissionCorrectionDto {
  @ApiProperty({ example: 'Here are some grammar corrections...', description: 'Correction text' })
  @IsNotEmpty()
  @IsString()
  correction: string;
}

/**
 * DTO for assigning a score to a mission diary entry
 * @deprecated Use AddMissionCorrectionWithScoreDto instead
 */
export class AssignMissionScoreDto {
  @ApiProperty({ example: 85, description: 'Score for the entry' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  score: number;
}

/**
 * DTO for adding correction with score to a mission diary entry
 */
export class AddMissionCorrectionWithScoreDto {
  @ApiProperty({ example: 'Here are some grammar corrections...', description: 'Correction text' })
  @IsNotEmpty()
  @IsString()
  correction: string;

  @ApiProperty({ example: 85, description: 'Score for the entry' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  score: number;
}

/**
 * DTO for filtering mission diary entries
 */
export class MissionEntryFilterDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Filter by mission ID', required: false })
  @IsOptional()
  @IsUUID()
  missionId?: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Filter by student ID', required: false })
  @IsOptional()
  @IsUUID()
  studentId?: string;

  @ApiProperty({
    example: 'SUBMITTED',
    description: 'Filter by status (NEW, SUBMITTED, REVIEWED, CONFIRMED)',
    required: false,
    enum: MissionEntryStatus,
    enumName: 'MissionEntryStatus'
  })
  @IsOptional()
  status?: MissionEntryStatus;

  @ApiProperty({ example: '2023-08-01', description: 'Filter by creation date (from)', required: false })
  @IsOptional()
  createdAtFrom?: string;

  @ApiProperty({ example: '2023-08-31', description: 'Filter by creation date (to)', required: false })
  @IsOptional()
  createdAtTo?: string;
}

// Response DTOs

/**
 * DTO for mission feedback response
 */
export class MissionFeedbackResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Feedback ID' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mission entry ID' })
  missionEntryId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Tutor ID' })
  tutorId: string;

  @ApiProperty({ example: 'John Doe', description: 'Tutor name', required: false })
  tutorName?: string;

  @ApiProperty({ example: 'Great job on your analysis of the book!', description: 'Feedback text' })
  feedback: string;

  @ApiProperty({ example: 4, description: 'Rating (1-5)', required: false })
  rating?: number;

  @ApiProperty({ example: '2023-08-15T14:30:00Z', description: 'When the feedback was created' })
  createdAt: Date;
}

/**
 * DTO for mission diary entry response
 */
export class MissionDiaryEntryResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Entry ID' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mission ID' })
  missionId: string;

  @ApiProperty({ type: DiaryMissionResponseDto, description: 'Mission details', required: false })
  mission?: DiaryMissionResponseDto;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Student ID' })
  studentId: string;

  @ApiProperty({ example: 'Jane Smith', description: 'Student name', required: false })
  studentName?: string;

  @ApiProperty({ example: 'My favorite book is...', description: 'Content of the diary entry' })
  content: string;

  @ApiProperty({ example: 180, description: 'Word count of the entry' })
  wordCount: number;

  @ApiProperty({ example: 90, description: 'Progress percentage (0-100)' })
  progress: number;

  @ApiProperty({
    example: 'SUBMITTED',
    description: 'Status of the entry (NEW, SUBMITTED, REVIEWED, CONFIRMED)',
    enum: MissionEntryStatus,
    enumName: 'MissionEntryStatus'
  })
  status: MissionEntryStatus;

  @ApiProperty({ example: 85, description: 'Score gained for the entry', required: false })
  gainedScore?: number;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the tutor who reviewed the entry', required: false })
  reviewedBy?: string;

  @ApiProperty({ example: 'John Doe', description: 'Name of the tutor who reviewed the entry', required: false })
  reviewerName?: string;

  @ApiProperty({ example: '2023-08-16T10:15:00Z', description: 'When the entry was reviewed', required: false })
  reviewedAt?: Date;

  @ApiProperty({ example: 'Here are some grammar corrections...', description: 'Correction text', required: false })
  correction?: string;

  @ApiProperty({ example: '2023-08-16T09:45:00Z', description: 'When the correction was provided', required: false })
  correctionProvidedAt?: Date;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'ID of the tutor who provided the correction', required: false })
  correctionProvidedBy?: string;

  @ApiProperty({ type: [MissionFeedbackResponseDto], description: 'Feedback comments', required: false })
  feedbacks?: MissionFeedbackResponseDto[];

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Skin ID assigned to the entry', required: false })
  skinId?: string;

  @ApiProperty({ type: DiarySkinResponseDto, description: 'Skin information for the entry', required: false })
  skin?: DiarySkinResponseDto;

  @ApiProperty({ example: '2023-08-15T12:00:00Z', description: 'When the entry was created' })
  createdAt: Date;

  @ApiProperty({ example: '2023-08-15T14:30:00Z', description: 'When the entry was last updated' })
  updatedAt: Date;

  // New fields for version tracking
  @ApiProperty({
    description: 'Original version that was reviewed by tutor (before any subsequent updates)',
    required: false,
    type: Object,
    example: {
      content: 'Original content when tutor reviewed',
      versionNumber: 1,
      createdAt: '2023-08-15T12:00:00Z'
    }
  })
  originalReviewedVersion?: {
    content: string;
    versionNumber: number;
    createdAt: Date;
  };

  @ApiProperty({ description: 'Total number of edit history records', example: 5, required: false })
  totalEditHistory?: number;
}

/**
 * DTO for mission diary entry version metadata
 */
export class MissionDiaryEntryVersionMetaDataDto {
  @ApiProperty({ example: '***********', description: 'IP address of the user', required: false })
  ipAddress?: string;

  @ApiProperty({ example: 'Mozilla/5.0...', description: 'User agent string', required: false })
  userAgent?: string;

  @ApiProperty({ example: 1250, description: 'Content length in characters', required: false })
  contentLength?: number;

  @ApiProperty({ example: 50, description: 'Change in content length', required: false })
  contentLengthDiff?: number;

  @ApiProperty({ example: true, description: 'Whether this was a significant change', required: false })
  significantChange?: boolean;

  @ApiProperty({ example: 15, description: 'Minutes since last update', required: false })
  timeFromLastUpdate?: number;

  @ApiProperty({ example: 'update', description: 'What triggered this update', required: false, enum: ['update', 'submit', 'restore'] })
  updateTrigger?: 'update' | 'submit' | 'restore';

  @ApiProperty({ example: 10, description: 'Change in word count', required: false })
  wordCountChange?: number;

  @ApiProperty({ example: true, description: 'Whether new paragraphs were added', required: false })
  hasNewParagraphs?: boolean;

  @ApiProperty({ example: 25, description: 'Edit distance between versions', required: false })
  editDistance?: number;
}

/**
 * DTO for mission diary entry version
 */
export class MissionDiaryEntryVersionDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Version ID' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mission entry ID' })
  missionEntryId: string;

  @ApiProperty({ example: 'My favorite book is...', description: 'Content of this version' })
  content: string;

  @ApiProperty({ example: 2, description: 'Version number' })
  versionNumber: number;

  @ApiProperty({ example: true, description: 'Whether this is the latest version' })
  isLatest: boolean;

  @ApiProperty({ example: 180, description: 'Word count of this version' })
  wordCount: number;

  @ApiProperty({ type: MissionDiaryEntryVersionMetaDataDto, description: 'Version metadata', required: false })
  metaData?: MissionDiaryEntryVersionMetaDataDto;

  @ApiProperty({ example: '2023-08-15T14:30:00Z', description: 'When this version was created' })
  createdAt: Date;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'User who created this version' })
  createdBy: string;
}

/**
 * DTO for mission diary entry version history response
 */
export class MissionDiaryEntryHistoryResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Mission entry ID' })
  missionEntryId: string;

  @ApiProperty({ example: 5, description: 'Total number of edit history records' })
  totalEditHistory: number;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-************', description: 'Current version ID', required: false })
  currentVersionId?: string;

  @ApiProperty({ type: [MissionDiaryEntryVersionDto], description: 'List of all versions' })
  versions: MissionDiaryEntryVersionDto[];
}
