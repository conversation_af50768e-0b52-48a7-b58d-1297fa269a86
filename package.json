{"name": "hec.api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "npx tsc -p tsconfig.json", "build:prod": "npx webpack --config webpack.prod.js", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "npx nest start", "start:dev": "npx nest start --watch", "start:debug": "npx nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "prestart:prod": "npm run build:prod", "start:ts-node": "cross-env NODE_ENV=production ts-node src/main.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:payment": "ts-node test/run-payment-tests.ts", "test:payment:unit": "jest --testPathPattern=\"payment.*spec.ts\"", "test:payment:integration": "jest --config ./test/jest-e2e.json --testPathPattern=\"payment-integration\"", "typeorm": "typeorm-ts-node-commonjs", "migration:create": "npx typeorm-ts-node-commonjs migration:create", "migration:generate": "npx typeorm-ts-node-commonjs migration:generate -d ./src/config/database.config.ts", "migration:run": "npx typeorm-ts-node-commonjs migration:run -d src/config/database.config.ts", "migration:revert": "npx typeorm-ts-node-commonjs migration:revert -d src/config/database.config.ts", "seed": "ts-node src/scripts/run-seeder.ts", "test:comprehensive": "ts-node scripts/run-comprehensive-test.ts", "backfill:novel-versions": "ts-node scripts/backfill-novel-versions.ts", "novel:diagnostic": "ts-node scripts/novel-version-diagnostic.ts", "novel:backfill-robust": "ts-node scripts/robust-novel-version-backfill.ts", "debug:novel-versions": "ts-node scripts/debug-novel-versions.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.817.0", "@aws-sdk/s3-request-presigner": "^3.817.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.1", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.6", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.0", "@types/express": "^4.17.21", "@types/multer": "^1.4.12", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cross-env": "^7.0.3", "dotenv": "^16.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.14.0", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.7.4", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.21", "typeorm-extension": "^3.7.0", "winston": "^3.17.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/qrcode": "^1.5.5", "@types/socket.io": "^3.0.2", "@types/supertest": "^6.0.2", "cross-env": "^7.0.3", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "ignore-loader": "^0.1.2", "jest": "^29.7.0", "prettier": "^3.4.2", "run-script-webpack-plugin": "^0.2.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "^7.0.0", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}