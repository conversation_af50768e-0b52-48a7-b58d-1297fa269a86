import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { DiarySettingsService } from './diary-settings.service';
import { DiaryReviewService } from './diary-review.service';
import { DiaryEntryService } from './diary-entry.service';
import { DiarySkinService } from './diary-skin.service';
import { DiaryShareService } from './diary-share.service';
import { DiaryAwardService } from './diary-award.service';
import { DiaryTutorService } from './diary-tutor.service';
import { DiaryMapperService } from './diary-mapper.service';
// FileRegistryService is now used by sub-services
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';

import 'multer';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { DiaryAwardPeriod } from '../../database/entities/diary-award.entity';

import {
  CreateDiaryEntryDto,
  UpdateDiaryEntryDto,
  DiaryEntryResponseDto,
  TutorDiaryEntryResponseDto,
  CreateDiaryFeedbackDto,
  DiaryFeedbackResponseDto,
  ShareDiaryEntryDto,
  DiaryShareResponseDto,
  DiarySkinResponseDto,
  PendingReviewEntryDto,
  StudentTutorListResponseDto,
  DiaryAwardsResponseDto,
  DiaryTopScorersResponseDto,
  DiaryPeriodAwardsResponseDto,
  DiaryEntryFilterDto,
  DiaryDetailsDto,
  CreateDiarySkinDto,
  UpdateDiarySkinDto,
  CreateStudentDiarySkinDto,
  UpdateStudentDiarySkinDto,
  SubmitDiaryEntryDto,
  DiaryEntryHistoryResponseDto,
  DiaryEntryVersionDto,
} from '../../database/models/diary.dto';
import { FeedbackOnlyDto } from '../../database/models/feedback-only.dto';
import { AdminDiaryEntryFilterDto } from 'src/database/models/admin-diary.dto';

@Injectable()
export class DiaryService {
  private readonly logger = new Logger(DiaryService.name);

  constructor(
    private readonly dataSource: DataSource,
    @Inject(forwardRef(() => DiarySettingsService))
    private readonly diarySettingsService: DiarySettingsService,
    @Inject(forwardRef(() => DiaryReviewService))
    private readonly diaryReviewService: DiaryReviewService,
    @Inject(forwardRef(() => DiaryEntryService))
    private readonly diaryEntryService: DiaryEntryService,
    @Inject(forwardRef(() => DiarySkinService))
    private readonly diarySkinService: DiarySkinService,
    @Inject(forwardRef(() => DiaryShareService))
    private readonly diaryShareService: DiaryShareService,
    @Inject(forwardRef(() => DiaryAwardService))
    private readonly diaryAwardService: DiaryAwardService,
    @Inject(forwardRef(() => DiaryTutorService))
    private readonly diaryTutorService: DiaryTutorService,
    @Inject(forwardRef(() => DiaryMapperService))
    private readonly diaryMapperService: DiaryMapperService,
  ) {}

  // ===== Diary Settings Template Methods =====

  /**
   * Create a new diary settings template
   * @param createDiarySettingsTemplateDto Data for creating a new diary settings template
   * @returns The created diary settings template
   */
  async createDiarySettingsTemplate(createDiarySettingsTemplateDto: any): Promise<any> {
    return this.diarySettingsService.createDiarySettingsTemplate(createDiarySettingsTemplateDto);
  }

  /**
   * Get all diary settings templates
   * @param paginationDto Pagination parameters
   * @returns A paged list of diary settings templates
   */
  async getAllDiarySettingsTemplates(paginationDto?: PaginationDto): Promise<any> {
    return this.diarySettingsService.getAllDiarySettingsTemplates(paginationDto);
  }

  /**
   * Get active diary settings templates
   * @param paginationDto Pagination parameters
   * @returns A paged list of active diary settings templates
   */
  async getActiveDiarySettingsTemplates(paginationDto?: PaginationDto): Promise<any> {
    return this.diarySettingsService.getActiveDiarySettingsTemplates(paginationDto);
  }

  /**
   * Get a diary settings template by ID
   * @param id The ID of the diary settings template
   * @returns The diary settings template
   */
  async getDiarySettingsTemplateById(id: string): Promise<any> {
    return this.diarySettingsService.getDiarySettingsTemplateById(id);
  }

  /**
   * Update a diary settings template
   * @param id The ID of the diary settings template
   * @param updateDiarySettingsTemplateDto Data for updating the diary settings template
   * @returns The updated diary settings template
   */
  async updateDiarySettingsTemplate(id: string, updateDiarySettingsTemplateDto: any): Promise<any> {
    return this.diarySettingsService.updateDiarySettingsTemplate(id, updateDiarySettingsTemplateDto);
  }

  /**
   * Delete a diary settings template
   * @param id The ID of the diary settings template
   */
  async deleteDiarySettingsTemplate(id: string): Promise<void> {
    return this.diarySettingsService.deleteDiarySettingsTemplate(id);
  }

  // ===== Diary Review Methods =====

  /**
   * Submit a correction for a diary entry
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor submitting the correction
   * @param createDiaryCorrectionDto The correction data
   * @returns The created correction
   */
  async submitCorrection(entryId: string, tutorId: string, createDiaryCorrectionDto: any): Promise<any> {
    return this.diaryReviewService.submitCorrection(entryId, tutorId, createDiaryCorrectionDto);
  }

  /**
   * Confirm a diary entry review
   * @param entryId The ID of the diary entry
   * @param tutorId The ID of the tutor confirming the review
   * @returns The updated diary entry response DTO (without user info for tutors)
   */
  async confirmDiaryEntryReview(entryId: string, tutorId: string): Promise<TutorDiaryEntryResponseDto> {
    const entry = await this.diaryReviewService.confirmDiaryEntryReview(entryId, tutorId);
    // Use tutor-specific mapper to exclude user details
    return this.diaryMapperService.mapEntryToTutorResponseDto(entry);
  }


  /**
   * Update the tutor greeting message for a student's diary
   * @param userId Student user ID
   * @param greeting Greeting message for the tutor
   * @returns Updated diary
   */
  async updateTutorGreeting(userId: string, greeting: string): Promise<Diary> {
    return this.diaryTutorService.updateTutorGreeting(userId, greeting);
  }  // Get or create a diary for a student
  async getOrCreateDiary(userId: string): Promise<Diary> {
    return this.diaryEntryService.getOrCreateDiary(userId);
  }

  // Get a diary for a student (without creating if it doesn't exist)
  async getDiary(userId: string): Promise<Diary | null> {
    try {
      const diary = await this.dataSource.getRepository(Diary).findOne({
        where: { userId: userId },
        relations: ['defaultSkin', 'user'],
        select: {
          id: true,
          userId: true,
          tutorGreeting: true,
          defaultSkinId: true,
          createdAt: true,
          updatedAt: true
        }
      });
      return diary;
    } catch (error) {
      this.logger.error(`Error getting diary for user ${userId}: ${error.message}`);
      return null;
    }
  }

  // Get all diary skins - delegated to DiarySkinService
  async getDiarySkins(includeInactive: boolean = false, studentId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<DiarySkinResponseDto>> {
    return this.diarySkinService.getDiarySkins(includeInactive, studentId, paginationDto);
  }

  // Create a new diary skin (Admin only) - delegated to DiarySkinService
  async createDiarySkin(adminId: string, createDiarySkinDto: CreateDiarySkinDto, previewImage: any): Promise<DiarySkinResponseDto> {
    return this.diarySkinService.createDiarySkin(adminId, createDiarySkinDto, previewImage);
  }

  // Get a diary skin by ID - delegated to DiarySkinService
  async getDiarySkinById(id: string): Promise<DiarySkinResponseDto> {
    return this.diarySkinService.getDiarySkinById(id);
  }

  // Update a diary skin (Admin only) - delegated to DiarySkinService
  async updateDiarySkin(id: string, _adminId: string, updateDiarySkinDto: UpdateDiarySkinDto, previewImage?: any): Promise<DiarySkinResponseDto> {
    // adminId is passed to the controller but not needed in the service (prefixed with _ to indicate it's unused)
    return this.diarySkinService.updateDiarySkin(id, updateDiarySkinDto, previewImage);
  }

  // Create a new student diary skin - delegated to DiarySkinService
  async createStudentDiarySkin(studentId: string, createStudentDiarySkinDto: CreateStudentDiarySkinDto, previewImage: any): Promise<DiarySkinResponseDto> {
    return this.diarySkinService.createStudentDiarySkin(studentId, createStudentDiarySkinDto, previewImage);
  }

  // Get a student diary skin by ID - delegated to DiarySkinService
  async getStudentDiarySkinById(id: string, studentId: string): Promise<DiarySkinResponseDto> {
    return this.diarySkinService.getStudentDiarySkinById(id, studentId);
  }

  // Update a student diary skin - delegated to DiarySkinService
  async updateStudentDiarySkin(id: string, studentId: string, updateStudentDiarySkinDto: UpdateStudentDiarySkinDto, previewImage?: any): Promise<DiarySkinResponseDto> {
    return this.diarySkinService.updateStudentDiarySkin(id, studentId, updateStudentDiarySkinDto, previewImage);
  }

  // Delete a student diary skin - delegated to DiarySkinService
  async deleteStudentDiarySkin(id: string, studentId: string): Promise<void> {
    return this.diarySkinService.deleteStudentDiarySkin(id, studentId);
  }

  /**
   * Get the diary module feature ID
   * This is used to identify the diary module in the tutor matching system
   * @returns The diary module feature ID or null if not found
   */
  async getDiaryModuleFeatureId(): Promise<string | null> {
    try {
      // Import the PlanFeature entity dynamically to avoid circular dependencies
      const { PlanFeature, FeatureType } = require('../../database/entities/plan-feature.entity');

      // Get the repository for PlanFeature
      const planFeatureRepository = this.dataSource.getRepository(PlanFeature);

      // Find the diary module feature
      const diaryFeature = await planFeatureRepository.findOne({
        where: { type: FeatureType.HEC_USER_DIARY }
      });

      if (!diaryFeature) {
        this.logger.warn('Diary module feature not found');
        return null;
      }

      return diaryFeature.id;
    } catch (error) {
      this.logger.error(`Error getting diary module feature ID: ${error.message}`, error.stack);
      return null;
    }
  }
  // Set the default skin for a user's diary - delegated to DiarySkinService
  async setDefaultDiarySkin(userId: string, skinId: string): Promise<void> {
    return this.diarySkinService.setDefaultDiarySkin(userId, skinId);
  }

  // Get the default diary skin for a user - delegated to DiarySkinService
  async getDefaultDiarySkin(userId: string): Promise<DiarySkinResponseDto | null> {
    return this.diarySkinService.getDefaultDiarySkin(userId);
  }
  // Delete a diary skin (Admin only) - delegated to DiarySkinService
  async deleteDiarySkin(id: string, adminId: string): Promise<void> {
    return this.diarySkinService.deleteDiarySkinAsAdmin(id, adminId);
  }

  /**
   * Create a new diary entry
   * @param userId The ID of the user creating the entry
   * @param createDiaryEntryDto Data for creating a new diary entry
   * @returns The created diary entry
   */
  async createDiaryEntry(userId: string, createDiaryEntryDto: CreateDiaryEntryDto): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.createDiaryEntry(userId, createDiaryEntryDto);
  }

  /**
   * Add a thanks message to a diary entry
   * @param id The ID of the diary entry
   * @param userId The ID of the user adding the thanks message
   * @param thanksMessage The thanks message to add
   * @returns The updated diary entry
   */
  async addThanksMessage(id: string, userId: string, thanksMessage: string): Promise<void> {
    return this.diaryEntryService.addThanksMessage(id, userId, thanksMessage);
  }


  async getDiaryEntry(entryId: string, userId: string): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.getDiaryEntry(entryId, userId);
  }


  async getTodaysDiaryEntry(userId: string): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.getTodaysDiaryEntry(userId);
  }


  async updateDiaryEntry(entryId: string, userId: string, updateDiaryEntryDto: UpdateDiaryEntryDto, request?: any): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.updateDiaryEntry(entryId, userId, updateDiaryEntryDto, request);
  }


  async updateTodaysDiarySkin(userId: string, skinId: string): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.updateTodaysDiarySkin(userId, skinId);
  }


  async submitDiaryEntry(entryId: string, userId: string, submitDto?: SubmitDiaryEntryDto): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.submitDiaryEntry(entryId, userId, submitDto);
  }

  // Version History Methods
  async getDiaryEntryHistory(entryId: string, userId: string): Promise<DiaryEntryHistoryResponseDto> {
    return this.diaryEntryService.getDiaryEntryHistory(entryId, userId);
  }

  async getDiaryEntryVersion(versionId: string, userId: string): Promise<DiaryEntryVersionDto> {
    return this.diaryEntryService.getDiaryEntryVersion(versionId, userId);
  }

  async restoreDiaryEntryVersion(entryId: string, versionId: string, userId: string): Promise<DiaryEntryResponseDto> {
    return this.diaryEntryService.restoreDiaryEntryVersion(entryId, versionId, userId);
  }
  // Get pending review entries
  async getPendingReviewEntries(tutorId?: string, paginationDto?: PaginationDto): Promise<PagedListDto<PendingReviewEntryDto>> {
    return this.diaryReviewService.getPendingReviewEntries(tutorId, paginationDto);
  }

  // Pick an entry for review
  async pickEntryForReview(entryId: string, tutorId: string): Promise<TutorDiaryEntryResponseDto> {
    const entry = await this.diaryReviewService.pickEntryForReview(entryId, tutorId);
    // Use tutor-specific mapper to exclude user details
    return this.diaryMapperService.mapEntryToTutorResponseDto(entry);
  }

  // Helper method to map Diary entity to DiaryDetailsDto
  private async mapDiaryToDetailsDto(diary: Diary): Promise<DiaryDetailsDto> {
    return this.diaryMapperService.mapDiaryToDetailsDto(diary);
  }
  // Helper method to map DiaryEntry entity to DiaryEntryResponseDto
  async mapEntryToResponseDto(entry: DiaryEntry, userId?: string): Promise<DiaryEntryResponseDto> {
    const baseResponse = await this.diaryMapperService.mapEntryToResponseDto(entry);

    // Update hasLiked if userId is provided
    if (userId && entry.likes) {
      baseResponse.hasLiked = entry.likes.some(like => like.likerId === userId);
    }

    return baseResponse;
  }

  // This method is now delegated to DiaryReviewService

  async submitFeedback(entryId: string, tutorId: string, createDiaryFeedbackDto: CreateDiaryFeedbackDto): Promise<DiaryFeedbackResponseDto> {
    return this.diaryReviewService.submitFeedback(entryId, tutorId, createDiaryFeedbackDto);
  }

  // It only accepts feedback text, not rating or award
  async submitFeedbackOnly(entryId: string, tutorId: string, feedbackOnlyDto: FeedbackOnlyDto): Promise<DiaryFeedbackResponseDto> {
    return this.diaryReviewService.submitFeedbackOnly(entryId, tutorId, feedbackOnlyDto);
  }

  // Share a diary entry - delegated to DiaryShareService
  async shareDiaryEntry(entryId: string, userId: string, shareDiaryEntryDto: ShareDiaryEntryDto): Promise<DiaryShareResponseDto> {
    return this.diaryShareService.shareDiaryEntry(entryId, userId, shareDiaryEntryDto);
  }

  // Get a shared diary entry by token - delegated to DiaryShareService
  async getSharedDiaryEntry(shareToken: string): Promise<DiaryEntryResponseDto> {
    return this.diaryShareService.getSharedDiaryEntry(shareToken, this.mapEntryToResponseDto.bind(this));
  }
// Get all publicly shared diary entries - delegated to DiaryShareService
async getPubliclySharedEntries(
  paginationDto: PaginationDto,
  userId?: string
): Promise<PagedListDto<DiaryEntryResponseDto>> {
  const { page = 1, limit = 10 } = paginationDto;
  const result = await this.diaryShareService.getPubliclySharedEntries(
    { page, limit },
    (entry: DiaryEntry) => this.mapEntryToResponseDto(entry, userId)
  );

  return {
    items: result.data,
    totalCount: result.total,
    totalItems: result.total,
    itemsPerPage: result.limit,
    currentPage: result.page,
    totalPages: Math.ceil(result.total / result.limit),
  };
}
  // Get all diary entries for a student - delegated to DiaryEntryService
  async getStudentDiaryEntries(
    userId: string,
    paginationDto?: PaginationDto,
    includeFriends: boolean = false
  ): Promise<PagedListDto<DiaryEntryResponseDto>> {
    return this.diaryEntryService.getStudentDiaryEntries(userId, paginationDto, includeFriends);
  }

  // Make a diary entry private - delegated to DiaryShareService
  async makeEntryPrivate(entryId: string, userId: string): Promise<void> {
    return this.diaryShareService.makeEntryPrivate(entryId, userId);
  }

  // Get a list of tutors who have reviewed a student's diary entries
  async getStudentTutors(userId: string): Promise<StudentTutorListResponseDto> {
    return this.diaryTutorService.getStudentTutors(userId);
  }

  // Get a summary of awards received by a student
  async getStudentAwards(userId: string): Promise<DiaryAwardsResponseDto> {
    return this.diaryAwardService.getStudentAwards(userId);
  }

  // Get top scorers for a specific period (weekly or monthly) - delegated to DiaryAwardService
  async getTopScorers(period: DiaryAwardPeriod): Promise<DiaryTopScorersResponseDto> {
    return this.diaryAwardService.getTopScorers(period);
  }

  // Generate awards for top scorers (to be called by a scheduled task) - delegated to DiaryAwardService
  async generatePeriodAwards(period: DiaryAwardPeriod): Promise<void> {
    return this.diaryAwardService.generatePeriodAwards(period);
  }

  // Get period awards for a student - delegated to DiaryAwardService
  async getStudentPeriodAwards(userId: string): Promise<DiaryPeriodAwardsResponseDto> {
    return this.diaryAwardService.getStudentPeriodAwards(userId);
  }



  async filterDiaryEntries(userId: string, filterDto: DiaryEntryFilterDto, paginationDto?: PaginationDto): Promise<PagedListDto<DiaryEntryResponseDto>> {
    return this.diaryEntryService.filterDiaryEntries(userId, filterDto, paginationDto);
  }

  /**
   * Get all diary entries with filtering (Admin only)
   * @param filterDto Filter parameters
   * @param paginationDto Pagination parameters
   * @returns A paged list of diary entries
   */
  async getAllDiaryEntries(
    filterDto: Omit<AdminDiaryEntryFilterDto, 'page' | 'limit' | 'sortBy'>,
    paginationDto: Pick<AdminDiaryEntryFilterDto, 'page' | 'limit' | 'sortBy'>
  ): Promise<PagedListDto<DiaryEntryResponseDto>> {
    const { page = 1, limit = 10, sortBy = 'createdAt' } = paginationDto;
    return this.diaryEntryService.getAllDiaryEntriesAsAdmin(filterDto, { page, limit, sortBy });
  }

  /**
   * Get a diary entry by ID (Admin only)
   * @param id The ID of the diary entry
   * @returns The diary entry
   */
  async getDiaryEntryAsAdmin(id: string): Promise<DiaryEntryResponseDto> {
    const entry = await this.diaryEntryService.getDiaryEntryByIdAsAdmin(id);
    return this.mapEntryToResponseDto(entry);
  }

  /**
   * Search diary entries by title or date
   * @param userId The ID of the user
   * @param filterDto Filter parameters (date and/or subject)
   * @param paginationDto Pagination parameters
   * @returns A paged list of filtered diary entries
   */
  async searchDiaryEntries(
    userId: string,
    filterDto: DiaryEntryFilterDto,
    paginationDto?: PaginationDto
  ): Promise<PagedListDto<DiaryEntryResponseDto>> {
    return this.diaryEntryService.filterDiaryEntries(userId, filterDto, paginationDto);
  }

  // Get the diary entry service instance
  getDiaryEntryService(): DiaryEntryService {
    return this.diaryEntryService;
  }

  /**
   * Fix missing original version for a specific entry
   * @param entryId The ID of the diary entry
   * @returns Result of the fix operation
   */
  async fixMissingOriginalVersionForEntry(entryId: string): Promise<any> {
    return this.diaryEntryService.fixMissingOriginalVersionForEntry(entryId);
  }

}
