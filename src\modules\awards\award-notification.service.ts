import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../../database/entities/user.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { Notification, NotificationType } from '../../database/entities/notification.entity';
import { EmailService } from '../email/email.service';

@Injectable()
export class AwardNotificationService {
  private readonly logger = new Logger(AwardNotificationService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(AwardWinner)
    private readonly awardWinnerRepository: Repository<AwardWinner>,
    @InjectRepository(Notification)
    private readonly notificationRepository: Repository<Notification>,
    private readonly emailService: EmailService,
  ) {}

  /**
   * Notify all award winners for a specific frequency and date range
   * @param frequency Award frequency (WEEKLY, MONTHLY, QUARTERLY, YEARLY)
   * @param startDate Start date of the award period
   * @param endDate End date of the award period
   */
  async notifyAwardWinners(
    frequency: string,
    startDate: Date,
    endDate: Date
  ): Promise<void> {
    try {
      this.logger.log(`Starting award winner notifications for ${frequency} period: ${startDate.toISOString()} to ${endDate.toISOString()}`);

      // Get all award winners for the specified period
      const awardWinners = await this.awardWinnerRepository
        .createQueryBuilder('winner')
        .leftJoinAndSelect('winner.user', 'user')
        .leftJoinAndSelect('winner.award', 'award')
        .where('winner.createdAt >= :startDate', { startDate })
        .andWhere('winner.createdAt <= :endDate', { endDate })
        .orderBy('winner.createdAt', 'DESC')
        .getMany();

      if (awardWinners.length === 0) {
        this.logger.log(`No award winners found for ${frequency} period`);
        return;
      }

      this.logger.log(`Found ${awardWinners.length} award winners to notify`);

      // Group winners by user to send consolidated notifications
      const winnersByUser = new Map<string, typeof awardWinners>();

      for (const winner of awardWinners) {
        const userId = winner.userId;
        if (!winnersByUser.has(userId)) {
          winnersByUser.set(userId, []);
        }
        winnersByUser.get(userId)!.push(winner);
      }

      // Send notifications to each user
      const notificationPromises = Array.from(winnersByUser.entries()).map(
        ([userId, userWinners]) => this.notifyUser(userId, userWinners, frequency)
      );

      await Promise.allSettled(notificationPromises);

      this.logger.log(`Award winner notifications completed for ${frequency} period`);
    } catch (error) {
      this.logger.error(`Error notifying award winners: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Notify a specific user about their award wins
   * @param userId User ID
   * @param winners Array of award winners for this user
   * @param frequency Award frequency
   */
  private async notifyUser(
    userId: string,
    winners: AwardWinner[],
    frequency: string
  ): Promise<void> {
    try {
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        this.logger.warn(`User ${userId} not found, skipping notification`);
        return;
      }

      // Create notification content based on number of awards
      const { title, message, htmlContent } = this.createNotificationContent(
        user.name,
        winners,
        frequency
      );

      // Create in-app notification
      const notification = this.notificationRepository.create({
        userId,
        type: NotificationType.AWARD_WINNER,
        title,
        message,
        isRead: false,
        relatedEntityType: 'award_winners',
        relatedEntityId: winners.map(w => w.id).join(','),
      });
      await this.notificationRepository.save(notification);

      // Send email notification
      await this.emailService.sendAwardWinnerEmail(
        user.email,
        user.name,
        title,
        message,
        htmlContent
      );

      this.logger.log(`Award notification sent to user ${user.name} (${userId}) for ${winners.length} award(s)`);
    } catch (error) {
      this.logger.error(`Error notifying user ${userId}: ${error.message}`, error.stack);
      // Don't rethrow to prevent disrupting other notifications
    }
  }

  /**
   * Create notification content based on award wins
   * @param userName User's name
   * @param winners Array of award winners
   * @param frequency Award frequency
   * @returns Notification content
   */
  private createNotificationContent(
    userName: string,
    winners: AwardWinner[],
    frequency: string
  ): { title: string; message: string; htmlContent: string } {
    const awardCount = winners.length;
    const frequencyText = frequency.toLowerCase();

    if (awardCount === 1) {
      const award = winners[0];
      return {
        title: `🏆 Congratulations! You Won "${award.award.name}"!`,
        message: `Congratulations ${userName}! You have won the "${award.award.name}" award for your outstanding performance in the ${frequencyText} period. You earned ${award.award.rewardPoints} reward points!`,
        htmlContent: this.createSingleAwardHtml(userName, award, frequencyText)
      };
    } else {
      const totalPoints = winners.reduce((sum, w) => sum + w.award.rewardPoints, 0);
      const awardNames = winners.map(w => w.award.name).join(', ');

      return {
        title: `🏆 Amazing! You Won ${awardCount} Awards!`,
        message: `Congratulations ${userName}! You have won ${awardCount} awards in the ${frequencyText} period: ${awardNames}. You earned a total of ${totalPoints} reward points!`,
        htmlContent: this.createMultipleAwardsHtml(userName, winners, frequencyText, totalPoints)
      };
    }
  }

  /**
   * Create HTML content for single award notification
   */
  private createSingleAwardHtml(userName: string, winner: AwardWinner, frequency: string): string {
    return `
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 64px; margin-bottom: 20px;">🏆</div>
        <h1 style="color: #333; margin-bottom: 10px;">Congratulations ${userName}!</h1>
        <h2 style="color: #667eea; margin-bottom: 20px;">${winner.award.name}</h2>
        <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <p style="font-size: 18px; color: #333; margin-bottom: 10px;">
            <strong>Award:</strong> ${winner.award.name}
          </p>
          <p style="font-size: 16px; color: #666; margin-bottom: 10px;">
            <strong>Period:</strong> ${frequency.charAt(0).toUpperCase() + frequency.slice(1)} Award
          </p>
          <p style="font-size: 16px; color: #666; margin-bottom: 10px;">
            <strong>Reward Points:</strong> ${winner.award.rewardPoints} points
          </p>
          <p style="font-size: 16px; color: #666;">
            <strong>Reason:</strong> ${winner.awardReason || 'Outstanding performance'}
          </p>
        </div>
        <div style="background: #667eea; color: white; padding: 15px 30px; border-radius: 25px; display: inline-block; font-weight: bold; margin-top: 20px;">
          🎉 Keep up the excellent work! 🎉
        </div>
      </div>
    `;
  }

  /**
   * Create HTML content for multiple awards notification
   */
  private createMultipleAwardsHtml(
    userName: string,
    winners: AwardWinner[],
    frequency: string,
    totalPoints: number
  ): string {
    const awardsList = winners.map(w => `
      <div style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #667eea;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <strong style="color: #333;">${w.award.name}</strong>
            <div style="color: #666; font-size: 14px;">${w.awardReason || 'Outstanding performance'}</div>
          </div>
          <div style="color: #667eea; font-weight: bold;">
            ${w.award.rewardPoints} pts
          </div>
        </div>
      </div>
    `).join('');

    return `
      <div style="text-align: center; padding: 20px;">
        <div style="font-size: 64px; margin-bottom: 20px;">🏆</div>
        <h1 style="color: #333; margin-bottom: 10px;">Amazing ${userName}!</h1>
        <h2 style="color: #667eea; margin-bottom: 20px;">You Won ${winners.length} Awards!</h2>
        <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
          <p style="font-size: 18px; color: #333; margin-bottom: 20px;">
            <strong>Period:</strong> ${frequency.charAt(0).toUpperCase() + frequency.slice(1)} Awards
          </p>
          <div style="text-align: left;">
            ${awardsList}
          </div>
          <div style="background: #667eea; color: white; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <strong>Total Reward Points: ${totalPoints}</strong>
          </div>
        </div>
        <div style="background: #667eea; color: white; padding: 15px 30px; border-radius: 25px; display: inline-block; font-weight: bold; margin-top: 20px;">
          🎉 Outstanding Performance! 🎉
        </div>
      </div>
    `;
  }
}
