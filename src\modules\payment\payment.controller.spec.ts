import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { PaymentController } from './payment.controller';
import { PaymentService } from './services/payment.service';
import { WebhookPayloadDto } from './dto/payment.dto';
import { PaymentTransactionStatus } from '../../database/entities/payment-transaction.entity';
import LoggerService from '../../common/services/logger.service';

describe('PaymentController', () => {
  let controller: PaymentController;

  const mockPaymentService = {
    initiatePayment: jest.fn(),
    getPaymentStatus: jest.fn(),
    processWebhook: jest.fn(),
    // refundPayment: jest.fn(), // Not implemented yet
  };

  const mockJwtService = {
    sign: jest.fn(),
    verify: jest.fn(),
  };

  const mockReflector = {
    get: jest.fn(),
    getAll: jest.fn(),
    getAllAndOverride: jest.fn(),
    getAllAndMerge: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockLoggerService = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
    verbose: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PaymentController],
      providers: [
        {
          provide: PaymentService,
          useValue: mockPaymentService,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: Reflector,
          useValue: mockReflector,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: LoggerService,
          useValue: mockLoggerService,
        },
      ],
    }).compile();

    controller = module.get<PaymentController>(PaymentController);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  // NOTE: initiatePayment method was removed from controller
  // Payment initiation is now handled by /shop/cart/checkout endpoint

  describe('getPaymentStatus', () => {
    const mockTransactionId = 'TXN-123';
    const mockStatusResponse = {
      transactionId: mockTransactionId,
      orderId: 'TEST-ORDER-123',
      status: PaymentTransactionStatus.COMPLETED,
      amount: 10000,
      currency: 'KRW',
      paymentMethod: 'card',
      createdAt: new Date(),
      processedAt: new Date(),
    };

    beforeEach(() => {
      mockPaymentService.getPaymentStatus.mockResolvedValue(mockStatusResponse);
    });

    it('should get payment status successfully', async () => {
      const result = await controller.getPaymentStatus(mockTransactionId);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockStatusResponse);
      expect(result.message).toBe('Payment status retrieved successfully');
    });

    it('should call payment service with correct transaction ID', async () => {
      await controller.getPaymentStatus(mockTransactionId);

      expect(mockPaymentService.getPaymentStatus).toHaveBeenCalledWith(mockTransactionId);
    });

    it('should handle payment service errors', async () => {
      const error = new Error('Transaction not found');
      mockPaymentService.getPaymentStatus.mockRejectedValue(error);

      await expect(
        controller.getPaymentStatus(mockTransactionId)
      ).rejects.toThrow(error);
    });
  });

  // Mock data for webhook tests
  const mockWebhookPayload: WebhookPayloadDto = {
    site_cd: 'TEST_SITE',
    tno: 'KCP-TXN-123',
    order_no: 'TEST-ORDER-123',
    tx_cd: 'TX00',
    tx_tm: new Date().toISOString(),
    // Optional legacy fields
    res_cd: '0000',
    res_msg: 'SUCCESS',
    ordr_idxx: 'TEST-ORDER-123',
    amount: '10000',
    good_name: 'Test Product',
    buyr_name: 'Test User',
    buyr_mail: '<EMAIL>',
    pay_method: '100000000000',
    app_time: new Date().toISOString(),
    app_no: 'APP-123',
  };

  const mockSignature = 'test-signature';
  const mockSourceIp = '127.0.0.1';

  describe('handleKcpWebhook', () => {

    beforeEach(() => {
      mockPaymentService.processWebhook.mockResolvedValue(undefined);
    });

    it('should handle webhook successfully', async () => {
      const result = await controller.handleKcpWebhook(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );

      expect(result).toBeDefined();
      expect(result.result).toBe('0000');
      expect(result.success).toBe(true);
      expect(result.message).toBe('Webhook processed successfully');
    });

    it('should call payment service with correct parameters', async () => {
      await controller.handleKcpWebhook(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );

      expect(mockPaymentService.processWebhook).toHaveBeenCalledWith(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );
    });

    // Removed duplicate test - webhook errors are handled gracefully, not thrown

    it('should handle missing signature', async () => {
      // The controller may not throw an error for missing signature
      // It depends on the actual implementation
      const result = await controller.handleKcpWebhook(mockWebhookPayload, undefined, mockSourceIp);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
    });
  });

  // Note: refundPayment method may not exist in the actual controller
  // This test is commented out until the method is implemented
  /*
  describe('refundPayment', () => {
    const mockRefundRequest = {
      transactionId: 'TXN-123',
      amount: 5000,
      reason: 'Customer request',
    };

    const mockRequest = {
      user: {
        id: 'user-123',
        sub: 'user-123',
        userId: 'testuser',
        email: '<EMAIL>',
        type: 'admin',
      }
    } as any;

    const mockRefundResponse = {
      success: true,
      refundId: 'REFUND-123',
      refundAmount: 5000,
      message: 'Refund processed successfully',
    };

    beforeEach(() => {
      mockPaymentService.refundPayment.mockResolvedValue(mockRefundResponse);
    });

    it('should process refund successfully', async () => {
      const result = await controller.refundPayment(mockRefundRequest, mockRequest);

      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockRefundResponse);
      expect(result.message).toBe('Refund processed successfully');
    });

    it('should call payment service with correct parameters', async () => {
      await controller.refundPayment(mockRefundRequest, mockRequest);

      expect(mockPaymentService.refundPayment).toHaveBeenCalledWith(
        mockRefundRequest,
        mockRequest.user.id
      );
    });

    it('should handle refund service errors', async () => {
      const error = new Error('Refund processing failed');
      mockPaymentService.refundPayment.mockRejectedValue(error);

      await expect(
        controller.refundPayment(mockRefundRequest, mockRequest)
      ).rejects.toThrow(error);
    });
  });
  */

  describe('error handling', () => {
    it('should handle webhook processing errors', async () => {
      const error = new Error('Webhook processing failed');
      mockPaymentService.processWebhook.mockRejectedValue(error);

      const result = await controller.handleKcpWebhook(
        mockWebhookPayload,
        mockSignature,
        mockSourceIp
      );

      expect(result).toBeDefined();
      expect(result.result).toBe('9999');
      expect(result.success).toBe(false);
      expect(result.message).toContain('Webhook processing failed');
    });
  });
});
