import { Controller, Get, Post, Body, Param, UseGuards, Query, Req, ForbiddenException } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { ShopService } from './shop.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';
import { StrictStudentOnly } from '../../common/decorators/strict-student-only.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse, ApiOkResponseWithPagedListType } from '../../common/decorators/api-response.decorator';
import { PagedListDto } from '../../common/models/paged-list.dto';
import { PaginationDto } from '../../common/models/pagination.dto';
import {
  ShopCategoryResponseDto,
  ShopItemResponseDto,
  ShopItemWithPromotionDto,
  PurchaseShopItemDto,
  ShopItemPurchaseResponseDto
} from '../../database/models/shop.dto';
import { ShopItemType } from '../../database/entities/shop-item.entity';
import { PaymentMethod, PurchaseStatus } from '../../database/entities/shop-item-purchase.entity';
import { Request } from 'express';

// Define a custom interface to extend the Express Request type
interface RequestWithUser extends Request {
  user: { id: string; [key: string]: any };
}

@ApiTags('shop')
@Controller('shop')
@UseGuards(JwtAuthGuard, StudentGuard, SubscriptionFeatureGuard)
@StrictStudentOnly()
@ApiBearerAuth('JWT-auth')
export class ShopController {
  constructor(private readonly shopService: ShopService) {}

  @Get('categories')
  @ApiOperation({
    summary: 'Get all shop categories',
    description: 'Get a list of all active shop categories.'
  })
  @ApiQuery({
    name: 'parentId',
    required: false,
    type: String,
    description: 'Filter categories by parent ID. Use "null" to get only top-level categories.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiOkResponseWithPagedListType(ShopCategoryResponseDto, 'Shop categories retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAllCategories(
    @Query('parentId') parentId?: string,
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<ShopCategoryResponseDto>>> {
    const categories = await this.shopService.getAllShopCategories(false, parentId, paginationDto);
    return ApiResponse.success(categories, 'Shop categories retrieved successfully');
  }

  @Get('categories/:id')
  @ApiOperation({
    summary: 'Get shop category by ID',
    description: 'Get details of a specific shop category by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    type: String
  })
  @ApiOkResponseWithType(ShopCategoryResponseDto, 'Shop category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop category not found')
  async getCategoryById(@Param('id') id: string): Promise<ApiResponse<ShopCategoryResponseDto>> {
    const category = await this.shopService.getShopCategoryById(id);
    return ApiResponse.success(category, 'Shop category retrieved successfully');
  }

  @Get('items')
  @ApiOperation({
    summary: 'Get all shop items',
    description: 'Get a list of all active shop items, optionally filtered by category and type.'
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter items by category ID'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ShopItemType,
    description: 'Filter items by type'
  })
  @ApiQuery({
    name: 'featuredOnly',
    required: false,
    type: Boolean,
    description: 'Whether to only include featured items'
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Shop items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAllItems(
    @Req() req: RequestWithUser,
    @Query('categoryId') categoryId?: string,
    @Query('type') type?: ShopItemType,
    @Query('featuredOnly') featuredOnly?: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getAllShopItems(
      categoryId,
      type,
      false,
      featuredOnly === 'true',
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    // Process each item to handle file paths and add purchase status
    const userId = req.user['id'];
    const purchasePromises = [];

    for (const item of pagedItems.items) {
      // Add a promise to check if the user has purchased this item
      if (item.type !== ShopItemType.FREE) {
        purchasePromises.push(
          this.shopService.hasUserPurchasedItem(userId, item.id)
            .then(hasPurchased => {
              item['isFreeOrPurchased'] = hasPurchased;
              return { itemId: item.id, hasPurchased };
            })
            .catch(() => ({ itemId: item.id, hasPurchased: false }))
        );
      } else {
        // Free items are always considered "purchased"
        item['isFreeOrPurchased'] = true;
      }

      // Handle file paths
      if (item.filePath) {
        // Free items always get a secure URL
        if (item.type === ShopItemType.FREE) {
          const secureUrl = await this.shopService.getSecureFileUrl(item.id);
          item.filePath = secureUrl;
          item['fileAvailable'] = true;
        } else {
          // For paid items, we'll update this after checking purchase status
          item['fileAvailable'] = false;
          item.filePath = null;
        }
      }
    }

    // Wait for all purchase checks to complete
    if (purchasePromises.length > 0) {
      const purchaseResults = await Promise.all(purchasePromises);

      // Update file paths for purchased items
      for (const result of purchaseResults) {
        if (result.hasPurchased) {
          const item = pagedItems.items.find(i => i.id === result.itemId);
          if (item && item.filePath === null && item['fileAvailable'] === false) {
            // Get the original item to get the file path
            const originalItem = await this.shopService.getShopItemById(item.id);
            if (originalItem.filePath) {
              const secureUrl = await this.shopService.getSecureFileUrl(item.id);
              item.filePath = secureUrl;
              item['fileAvailable'] = true;
            }
          }
        }
      }
    }

    return ApiResponse.success(pagedItems, 'Shop items retrieved successfully');
  }

  @Get('categories/:categoryId/items')
  @ApiOperation({
    summary: 'Get shop items by category',
    description: 'Get a list of shop items for a specific category with filtering and sorting options.'
  })
  @ApiParam({
    name: 'categoryId',
    description: 'Category ID',
    type: String
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match if 3+ characters)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Sort field (price, title, createdAt, etc.)'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Shop items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Category not found')
  async getItemsByCategory(
    @Req() req: RequestWithUser,
    @Param('categoryId') categoryId: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getShopItemsByCategory(
      categoryId,
      false, // includeInactive
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    // Process each item to handle file paths and add purchase status
    const userId = req.user['id'];
    const purchasePromises = [];

    for (const item of pagedItems.items) {
      // Add a promise to check if the user has purchased this item
      if (item.type !== ShopItemType.FREE) {
        purchasePromises.push(
          this.shopService.hasUserPurchasedItem(userId, item.id)
            .then(hasPurchased => {
              item['isFreeOrPurchased'] = hasPurchased;
              return { itemId: item.id, hasPurchased };
            })
            .catch(() => ({ itemId: item.id, hasPurchased: false }))
        );
      } else {
        // Free items are always considered "purchased"
        item['isFreeOrPurchased'] = true;
      }

      // Handle file paths
      if (item.filePath) {
        // Free items always get a secure URL
        if (item.type === ShopItemType.FREE) {
          const secureUrl = await this.shopService.getSecureFileUrl(item.id);
          item.filePath = secureUrl;
          item['fileAvailable'] = true;
        } else {
          // For paid items, we'll update this after checking purchase status
          item['fileAvailable'] = false;
          item.filePath = null;
        }
      }
    }

    // Wait for all purchase checks to complete
    if (purchasePromises.length > 0) {
      const purchaseResults = await Promise.all(purchasePromises);

      // Update file paths for purchased items
      for (const result of purchaseResults) {
        if (result.hasPurchased) {
          const item = pagedItems.items.find(i => i.id === result.itemId);
          if (item && item.filePath === null && item['fileAvailable'] === false) {
            // Get the original item to get the file path
            const originalItem = await this.shopService.getShopItemById(item.id);
            if (originalItem.filePath) {
              const secureUrl = await this.shopService.getSecureFileUrl(item.id);
              item.filePath = secureUrl;
              item['fileAvailable'] = true;
            }
          }
        }
      }
    }

    return ApiResponse.success(pagedItems, 'Shop items retrieved successfully');
  }

  @Get('items/with-promotions')
  @ApiOperation({
    summary: 'Get shop items with promotion details',
    description: 'Get a list of shop items with human-readable discount display.'
  })
  @ApiQuery({
    name: 'searchTerm',
    required: false,
    type: String,
    description: 'Search term for filtering across item number, title, category name, and promotion name (minimum 3 characters for partial matching, except for item numbers with hyphens)'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemWithPromotionDto, 'Shop items with promotions retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getItemsWithPromotions(
    @Query('searchTerm') searchTerm?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemWithPromotionDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getShopItemsWithPromotions(
      null, // categoryId
      null, // type
      false, // includeInactive
      false, // featuredOnly
      searchTerm,
      null, // promotionId
      paginationDto
    );

    return ApiResponse.success(pagedItems, 'Shop items with promotions retrieved successfully');
  }

  @Get('items/available')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get shop items available for purchase',
    description: 'Get a list of shop items that are available for purchase (not already owned by the student).'
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    type: String,
    description: 'Filter items by category ID'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ShopItemType,
    description: 'Filter items by type (free, in_app_purchase)'
  })
  @ApiQuery({
    name: 'featuredOnly',
    required: false,
    type: String,
    description: 'Filter to only include featured items (true/false)'
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Available shop items retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAvailableItems(
    @Req() req: RequestWithUser,
    @Query('categoryId') categoryId?: string,
    @Query('type') type?: ShopItemType,
    @Query('featuredOnly') featuredOnly?: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getShopItemsAvailableForPurchase(
      req.user['id'],
      categoryId,
      type,
      featuredOnly === 'true',
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    return ApiResponse.success(pagedItems, 'Available shop items retrieved successfully');
  }

  @Get('items/available/grouped')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get available shop items grouped by category',
    description: 'Get a list of shop items that are available for purchase, grouped by category.'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ShopItemType,
    description: 'Filter items by type (free, in_app_purchase)'
  })
  @ApiQuery({
    name: 'featuredOnly',
    required: false,
    type: String,
    description: 'Filter to only include featured items (true/false)'
  })
  @ApiOkResponseWithType(Object, 'Available shop items grouped by category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getAvailableItemsGrouped(
    @Req() req: RequestWithUser,
    @Query('type') type?: ShopItemType,
    @Query('featuredOnly') featuredOnly?: string
  ): Promise<ApiResponse<{ categories: { categoryId: string, categoryName: string, items: ShopItemResponseDto[] }[] }>> {
    const groupedItems = await this.shopService.getAvailableItemsGroupedByCategory(
      req.user['id'],
      type,
      featuredOnly === 'true'
    );

    // Convert the object to an array format for the response
    const categories = Object.entries(groupedItems).map(([categoryId, data]) => ({
      categoryId,
      categoryName: data.categoryName,
      items: data.items
    }));

    return ApiResponse.success({ categories }, 'Available shop items grouped by category retrieved successfully');
  }

  @Get('items/available/categories/:categoryId')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get available shop items by category',
    description: 'Get a list of shop items that are available for purchase in a specific category.'
  })
  @ApiParam({
    name: 'categoryId',
    description: 'Category ID',
    type: String
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: ShopItemType,
    description: 'Filter items by type (free, in_app_purchase)'
  })
  @ApiQuery({
    name: 'featuredOnly',
    required: false,
    type: String,
    description: 'Filter to only include featured items (true/false)'
  })
  @ApiQuery({
    name: 'itemNumber',
    required: false,
    type: String,
    description: 'Filter items by item number'
  })
  @ApiQuery({
    name: 'title',
    required: false,
    type: String,
    description: 'Filter items by title (partial match)'
  })
  @ApiQuery({
    name: 'promotionId',
    required: false,
    type: String,
    description: 'Filter items by promotion ID'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction'
  })
  @ApiOkResponseWithPagedListType(ShopItemResponseDto, 'Available shop items by category retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Category not found')
  async getAvailableItemsByCategory(
    @Req() req: RequestWithUser,
    @Param('categoryId') categoryId: string,
    @Query('type') type?: ShopItemType,
    @Query('featuredOnly') featuredOnly?: string,
    @Query('itemNumber') itemNumber?: string,
    @Query('title') title?: string,
    @Query('promotionId') promotionId?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC'
  ): Promise<ApiResponse<PagedListDto<ShopItemResponseDto>>> {
    // Create pagination DTO manually
    const paginationDto: PaginationDto = {
      page: page ? +page : 1,
      limit: limit ? +limit : 10,
      sortBy,
      sortDirection: sortDirection || 'DESC'
    };

    const pagedItems = await this.shopService.getAvailableItemsByCategory(
      req.user['id'],
      categoryId,
      type,
      featuredOnly === 'true',
      itemNumber,
      title,
      promotionId,
      paginationDto
    );

    return ApiResponse.success(pagedItems, 'Available shop items by category retrieved successfully');
  }

  @Get('items/:id/has-purchased')
  @ApiOperation({
    summary: 'Check if user has purchased a shop item',
    description: 'Check if the current user has purchased a specific shop item.'
  })
  @ApiParam({
    name: 'id',
    description: 'Shop item ID',
    type: String
  })
  @ApiOkResponseWithType(Boolean, 'Purchase status checked successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async hasUserPurchasedItem(@Param('id') id: string, @Req() req: RequestWithUser): Promise<ApiResponse<boolean>> {
    const hasPurchased = await this.shopService.hasUserPurchasedItem(req.user['id'], id);
    return ApiResponse.success(hasPurchased, 'Purchase status checked successfully');
  }

  @Get('items/:id/can-access')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Check if user can access item',
    description: 'Check if the current user can access/use a specific shop item (free items are always accessible, paid items require purchase).'
  })
  @ApiParam({
    name: 'id',
    description: 'Shop item ID',
    type: String
  })
  @ApiOkResponseWithType(Boolean, 'Access status checked successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async canUserAccessItem(@Param('id') id: string, @Req() req: RequestWithUser): Promise<ApiResponse<boolean>> {
    const canAccess = await this.shopService.canUserAccessItem(req.user['id'], id);
    return ApiResponse.success(canAccess, 'Access status checked successfully');
  }

  @Get('items/:id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get shop item by ID',
    description: 'Get details of a specific shop item by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Item ID',
    type: String
  })
  @ApiOkResponseWithType(ShopItemResponseDto, 'Shop item retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async getItemById(@Param('id') id: string, @Req() req: RequestWithUser): Promise<ApiResponse<ShopItemResponseDto>> {
    const item = await this.shopService.getShopItemById(id);

    // If the item has a file path, generate a secure URL for it
    if (item.filePath) {
      const userId = req.user['id'];

      // Check if the user can access the file (free items or purchased items)
      const canAccessFile = await this.shopService.canUserAccessItem(userId, id);

      if (canAccessFile) {
        // Generate a secure URL for the file
        const secureUrl = await this.shopService.getSecureFileUrl(id);
        item.filePath = secureUrl; // Replace the file path with the secure URL
      } else {
        // Don't provide the secure URL if the user cannot access the item
        item['fileAvailable'] = false;
        item.filePath = null;
      }
    }

    // Add a flag indicating if the item is accessible (free or purchased)
    item['isFreeOrPurchased'] = await this.shopService.canUserAccessItem(req.user['id'], id);

    return ApiResponse.success(item, 'Shop item retrieved successfully');
  }

  @Post('purchase')
  @ApiOperation({
    summary: 'Purchase a shop item',
    description: 'Purchase a shop item using reward points or other payment methods.'
  })
  @ApiBody({
    type: PurchaseShopItemDto,
    description: 'Purchase data'
  })
  @ApiOkResponseWithType(ShopItemPurchaseResponseDto, 'Shop item purchased successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async purchaseItem(
    @Req() req: RequestWithUser,
    @Body() purchaseShopItemDto: PurchaseShopItemDto
  ): Promise<ApiResponse<ShopItemPurchaseResponseDto>> {
    const userId = req.user['id'];

    // Check if the item is free
    const item = await this.shopService.getShopItemById(purchaseShopItemDto.shopItemId);

    if (item.type === ShopItemType.FREE) {
      // For free items, check if a purchase record already exists
      const hasPurchased = await this.shopService.hasUserPurchasedItem(userId, purchaseShopItemDto.shopItemId);

      if (hasPurchased) {
        // If already "purchased", just return success
        return ApiResponse.success(
          {
            id: 'free-item',
            userId,
            shopItemId: purchaseShopItemDto.shopItemId,
            shopItemTitle: item.title,
            originalPrice: 0,
            finalPrice: 0,
            paymentMethod: PaymentMethod.FREE,
            status: PurchaseStatus.COMPLETED,
          } as ShopItemPurchaseResponseDto,
          'Free item is already available'
        );
      }

      // Set payment method to FREE
      purchaseShopItemDto.paymentMethod = PaymentMethod.FREE;
    }

    // Process the purchase
    const purchase = await this.shopService.purchaseShopItem(userId, purchaseShopItemDto);
    return ApiResponse.success(purchase, 'Shop item purchased successfully');
  }

  @Get('purchases')
  @ApiOperation({
    summary: 'Get user purchases',
    description: 'Get a list of all purchases made by the current user.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page'
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by'
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    enum: ['ASC', 'DESC'],
    description: 'Sort direction (ASC or DESC)'
  })
  @ApiOkResponseWithPagedListType(ShopItemPurchaseResponseDto, 'User purchases retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getUserPurchases(
    @Req() req: RequestWithUser,
    @Query() paginationDto?: PaginationDto
  ): Promise<ApiResponse<PagedListDto<ShopItemPurchaseResponseDto>>> {
    const userId = req.user['id'];
    const pagedPurchases = await this.shopService.getUserPurchases(userId, paginationDto);

    // Add secure URLs for purchased items with file paths
    for (const purchase of pagedPurchases.items) {
      try {
        // Get the shop item details to access the file path
        const item = await this.shopService.getShopItemById(purchase.shopItemId);
        if (item && item.filePath) {
          // Generate a secure URL for the file
          const secureUrl = await this.shopService.getSecureFileUrl(purchase.shopItemId);
          // Add the secure URL to the purchase response
          purchase['secureFileUrl'] = secureUrl;
        }
      } catch (error) {
        // If there's an error getting the item, just continue without the secure URL
        console.warn(`Could not get secure URL for purchased item ${purchase.shopItemId}: ${error.message}`);
      }
    }

    return ApiResponse.success(pagedPurchases, 'User purchases retrieved successfully');
  }

  @Get('purchases/:id')
  @ApiOperation({
    summary: 'Get purchase by ID',
    description: 'Get details of a specific purchase by ID.'
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase ID',
    type: String
  })
  @ApiOkResponseWithType(ShopItemPurchaseResponseDto, 'Purchase retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Purchase not found')
  async getPurchaseById(@Param('id') id: string, @Req() req: RequestWithUser): Promise<ApiResponse<ShopItemPurchaseResponseDto>> {
    const purchase = await this.shopService.getPurchaseById(id);

    // Ensure user can only access their own purchases
    if (purchase.userId !== req.user['id']) {
      throw new ForbiddenException('You can only access your own purchases');
    }

    // Add secure URL for the purchased item if it has a file path
    try {
      // Get the shop item details to access the file path
      const item = await this.shopService.getShopItemById(purchase.shopItemId);
      if (item && item.filePath) {
        // Generate a secure URL for the file
        const secureUrl = await this.shopService.getSecureFileUrl(purchase.shopItemId);
        // Add the secure URL to the purchase response
        purchase['secureFileUrl'] = secureUrl;
      }
    } catch (error) {
      // If there's an error getting the item, just continue without the secure URL
      console.warn(`Could not get secure URL for purchased item ${purchase.shopItemId}: ${error.message}`);
    }

    return ApiResponse.success(purchase, 'Purchase retrieved successfully');
  }













  @Post('items/:id/apply-skin')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Apply a purchased skin to diary',
    description: 'Apply a purchased skin to the user\'s diary. Only works for skin items that have been purchased.'
  })
  @ApiParam({
    name: 'id',
    description: 'Shop item ID (must be a skin item)',
    type: String
  })
  @ApiOkResponseWithType(Object, 'Skin applied successfully')
  @ApiErrorResponse(400, 'Invalid input or item not purchased')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async applySkinToDiary(@Param('id') id: string, @Req() req: RequestWithUser): Promise<ApiResponse<{ success: boolean; message: string }>> {
    const result = await this.shopService.applyPurchasedSkinToDiary(req.user['id'], id);
    return ApiResponse.success(result, 'Skin applied to diary successfully');
  }
}
