# Payment Gateway Redirect API Mismatch - Analysis & Fix

## Issue Analysis

### Problem Identified
The payment gateway redirect API had a critical mismatch between the URL parameters being generated and what the redirect handler expected from the KCP payment gateway server.

### Root Cause
1. **Incorrect URL Generation**: The system was generating URLs pointing to its own redirect endpoint instead of the actual KCP payment gateway
2. **Parameter Mismatch**: Generated URLs included payment initiation parameters, but the redirect handler expected payment result parameters
3. **Flow Confusion**: The implementation mixed payment initiation flow with payment result handling

### Original Problematic Flow
```
Frontend → HEC Backend (/payment/kcp/redirect) → [Missing KCP Gateway]
```

### Expected Response from Your System
```json
{
  "paymentTransactionId": "TXN-1750141860417-DO2K3T",
  "paymentUrl": "http://**************:3010/payment/kcp/redirect?tno=TXN-1750141860437&ordr_idxx=ORDER-0b11ee95-4211-4fe2-9bf1-e37dc01f5ce5-1750141860369&amount=22.990000000000002&pay_method=100000000000&ordr_chk=e25630544eb66f59dbe7a273c83866fa828d431c00e395b0c9090e56fdf6dc5f&kcp_sign_data=SIGN-1750141860437"
}
```

### Issues with Original Implementation

#### 1. URL Generation Mismatch
**Before (Incorrect):**
```typescript
// Generated URL pointed to own backend
return `${baseUrl}/payment/kcp/redirect?${params.toString()}`;
// Result: http://localhost:3012/payment/kcp/redirect?tno=...&amount=...
```

**After (Fixed):**
```typescript
// Now points to actual KCP gateway
const kcpGatewayUrl = this.kcpConfig.getPaymentUrl();
return `${kcpGatewayUrl}?${params.toString()}`;
// Result: https://stg-spl.kcp.co.kr/gw/payment.jsp?site_cd=...&tno=...
```

#### 2. Parameter Mismatch
**Generated Parameters (Payment Initiation):**
- `tno`, `ordr_idxx`, `amount`, `pay_method`, `ordr_chk`, `kcp_sign_data`

**Expected Parameters (Payment Result):**
- `tno`, `ordr_idxx`, `res_cd`, `res_msg`

#### 3. Redirect Handler Issues
**Before:** Only handled payment results, couldn't handle missing `res_cd`
**After:** Handles both scenarios with proper validation and logging

## Fixes Implemented

### 1. Fixed URL Generation (`src/modules/payment/services/kcp.service.ts`)

```typescript
private generatePaymentUrl(tradeRegResponse: KcpTradeRegResponse, request: PaymentInitiationRequest): string {
  // Get the actual KCP payment gateway URL
  const kcpGatewayUrl = this.kcpConfig.getPaymentUrl();
  
  // Generate return URL that KCP will redirect to after payment
  const baseUrl = this.configService.get<string>('API_URL', 'http://localhost:3012');
  const returnUrl = `${baseUrl}/payment/kcp/redirect`;
  
  const params = new URLSearchParams({
    site_cd: this.kcpConfig.getSiteCd(),
    tno: tradeRegResponse.tno,
    ordr_idxx: request.orderId,
    good_name: request.productName,
    good_mny: request.amount.toString(),
    buyr_name: request.buyerName,
    buyr_mail: request.buyerEmail,
    buyr_tel1: request.buyerPhone,
    pay_method: this.getPayMethodCode(request.paymentMethod),
    currency: request.currency || 'KRW',
    ordr_chk: tradeRegResponse.ordr_chk,
    kcp_sign_data: tradeRegResponse.kcp_sign_data,
    Ret_URL: returnUrl, // This is where KCP will redirect after payment
    user_agent: 'HEC-Frontend/1.0'
  });

  return `${kcpGatewayUrl}?${params.toString()}`;
}
```

### 2. Enhanced Redirect Handler (`src/modules/payment/payment.controller.ts`)

```typescript
async handleKcpRedirect(@Req() req: Request, @Res() res: Response): Promise<void> {
  const queryParams = req.query;
  const { tno, ordr_idxx, res_cd, res_msg, amount, pay_method, ordr_chk, kcp_sign_data } = queryParams;

  this.logger.log(`KCP redirect received with params:`, queryParams);

  // Check if this is a response from KCP (contains res_cd) or initial redirect
  if (res_cd !== undefined) {
    // This is a response from KCP payment gateway
    this.logger.log(`KCP payment response - Transaction: ${tno}, Order: ${ordr_idxx}, Result: ${res_cd}`);

    if (res_cd === '0000') {
      // Success - redirect to success page
      const successUrl = `${process.env.FRONTEND_URL}/payment/success?transaction=${tno}&order=${ordr_idxx}`;
      this.logger.log(`Payment successful, redirecting to: ${successUrl}`);
      res.redirect(successUrl);
    } else {
      // Failure - redirect to failure page
      const failureUrl = `${process.env.FRONTEND_URL}/payment/failure?transaction=${tno}&order=${ordr_idxx}&error=${encodeURIComponent(res_msg || 'Payment failed')}`;
      this.logger.log(`Payment failed, redirecting to: ${failureUrl}`);
      res.redirect(failureUrl);
    }
  } else {
    // Handle backward compatibility or invalid requests
    this.logger.warn(`Received initial redirect parameters - this should not happen with new implementation`);
    
    if (!tno || !ordr_idxx) {
      this.logger.error(`Missing required parameters for KCP redirect: tno=${tno}, ordr_idxx=${ordr_idxx}`);
      const errorUrl = `${process.env.FRONTEND_URL}/payment/failure?error=${encodeURIComponent('Invalid payment parameters')}`;
      res.redirect(errorUrl);
      return;
    }

    const errorUrl = `${process.env.FRONTEND_URL}/payment/failure?transaction=${tno}&order=${ordr_idxx}&error=${encodeURIComponent('Invalid payment flow')}`;
    res.redirect(errorUrl);
  }
}
```

### 3. Updated KCP Configuration (`src/modules/payment/services/kcp-config.service.ts`)

```typescript
// Changed from API endpoint to payment page
paymentUrl: this.configService.get<string>('KCP_PAYMENT_URL', '/gw/payment.jsp'),
```

### 4. Updated Tests and Documentation

- Fixed test expectations to match new URL generation
- Updated documentation to reflect correct payment flow
- Added comprehensive logging for debugging

## Corrected Payment Flow

### New Correct Flow
```
1. Frontend → KCP Gateway (Direct)
2. User completes payment on KCP
3. KCP → HEC Backend (/payment/kcp/redirect) with result
4. HEC Backend → Frontend (success/failure page)
```

### URL Examples

**Generated Payment URL (Now Correct):**
```
https://stg-spl.kcp.co.kr/gw/payment.jsp?site_cd=TESTSITE&tno=TXN-1750141860437&ordr_idxx=ORDER-123&good_mny=22.99&pay_method=100000000000&ordr_chk=abc123&kcp_sign_data=SIGN-123&Ret_URL=http%3A//**************%3A3010/payment/kcp/redirect
```

**KCP Return URL (What redirect handler receives):**
```
http://**************:3010/payment/kcp/redirect?tno=TXN-1750141860437&ordr_idxx=ORDER-123&res_cd=0000&res_msg=SUCCESS
```

## Benefits of the Fix

1. **Correct Payment Flow**: Users now go directly to KCP gateway
2. **Proper Parameter Handling**: Redirect handler correctly processes KCP responses
3. **Better Error Handling**: Comprehensive logging and error scenarios
4. **Security**: Payment processing happens on KCP's secure servers
5. **Compliance**: Follows KCP's standard integration pattern

## Testing Recommendations

1. Test payment initiation with the new URL format
2. Verify KCP gateway loads correctly
3. Test successful payment flow
4. Test failed payment scenarios
5. Verify proper frontend redirects
6. Check logs for proper parameter handling

## Environment Variables to Verify

Ensure these are properly configured:
```env
KCP_SITE_CD=your_site_code
KCP_SITE_KEY=your_site_key
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_PAYMENT_URL=/gw/payment.jsp
API_URL=http://**************:3010
FRONTEND_URL=your_frontend_url
```
