import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { BaseFileRegistry } from './base-file-registry.entity';
import { StudentDiarySkin } from './student-diary-skin.entity';

@Entity()
export class StudentDiarySkinRegistry extends BaseFileRegistry {
  @Column({ name: 'student_diary_skin_id' })
  studentDiarySkinId: string;

  @ManyToOne(() => StudentDiarySkin, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'student_diary_skin_id' })
  studentDiarySkin: StudentDiarySkin;

  @Column({ name: 'student_id' })
  studentId: string;

  @Column({ name: 'user_id', nullable: true })
  userId: string;

  /**
   * Convert to DTO for API responses
   */
  toDto(): any {
    return {
      ...this.toSimpleObject(),
      studentDiarySkinId: this.studentDiarySkinId,
      studentId: this.studentId,
      userId: this.userId,
      studentDiarySkin: this.studentDiarySkin ? {
        id: this.studentDiarySkin.id,
        name: this.studentDiarySkin.name,
        description: this.studentDiarySkin.description,
        isActive: this.studentDiarySkin.isActive,
        studentId: this.studentDiarySkin.studentId
      } : null
    };
  }
}
