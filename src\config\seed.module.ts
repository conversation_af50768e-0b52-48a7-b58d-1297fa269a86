import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SeedService } from './seed.service';
import { User } from '../database/entities/user.entity';
import { Role } from '../database/entities/role.entity';
import { UserRole } from '../database/entities/user-role.entity';
import { Plan } from '../database/entities/plan.entity';
import { UserPlan } from '../database/entities/user-plan.entity';
import { DiarySkin } from '../database/entities/diary-skin.entity';
import { PlanFeature } from '../database/entities/plan-feature.entity';
import { Award } from '../database/entities/award.entity';
import { AwardWinner } from '../database/entities/award-winner.entity';
import { Promotion } from '../database/entities/promotion.entity';
import { ShopCategory } from '../database/entities/shop-category.entity';
import { ShopItem } from '../database/entities/shop-item.entity';
import { TutorApproval } from '../database/entities/tutor-approval.entity';
import { PromotionSeed } from './seeds/promotion.seed';
import { HecDiaryAwardsSeed } from './seeds/hec-diary-awards.seed';
import { EssayAwardsSeed } from './seeds/essay-awards.seed';
import { NovelAwardsSeed } from './seeds/novel-awards.seed';
import { ShopSeed } from './seeds/shop.seed';
import { ConfigModule } from '@nestjs/config';
import { WeekSeed } from './seeds/week.seed';
import { MonthSeed } from './seeds/month.seed';
import { QAMissionWeek } from '../database/entities/qa-mission-week.entity';
import { QAMissionMonth } from '../database/entities/qa-mission-month.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User, Role, UserRole, Plan, UserPlan, DiarySkin,
      PlanFeature, Award, AwardWinner, Promotion, ShopCategory,
      ShopItem, TutorApproval, QAMissionWeek, QAMissionMonth
    ]),
    ConfigModule,
  ],
  providers: [SeedService, PromotionSeed, HecDiaryAwardsSeed, EssayAwardsSeed, NovelAwardsSeed, ShopSeed, WeekSeed, MonthSeed],
  exports: [SeedService],
})
export class SeedModule {}
