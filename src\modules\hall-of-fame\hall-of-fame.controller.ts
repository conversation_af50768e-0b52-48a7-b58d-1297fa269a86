import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger,
  <PERSON>rseInt<PERSON>ipe,
  DefaultValuePipe
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth
} from '@nestjs/swagger';
import { HallOfFameService } from './hall-of-fame.service';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { UserType } from '../../database/entities/user.entity';
import { AwardModule, AwardFrequency } from '../../database/entities/award.entity';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { ApiResponse } from '../../common/dto/api-response.dto';
import {
  OngoingAwardsQueryDto,
  OngoingAwardsResponseDto,
  ModuleHallOfFameDto
} from '../../database/models/hall-of-fame.dto';

@ApiTags('Hall of Fame')
@Controller('hall-of-fame')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class HallOfFameController {
  private readonly logger = new Logger(HallOfFameController.name);

  constructor(private readonly hallOfFameService: HallOfFameService) {}



  @Get('diary')
  @ApiOperation({
    summary: 'Get Diary Hall of Fame',
    description: 'Get Hall of Fame for Diary module with award types and their configured frequencies (weekly, monthly, yearly)'
  })
  @ApiOkResponseWithType(ModuleHallOfFameDto, 'Diary Hall of Fame retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of winners to return (default: 50)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getDiaryHallOfFame(
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number
  ): Promise<ApiResponse<ModuleHallOfFameDto>> {
    this.logger.log(`Getting Diary Hall of Fame, limit: ${limit}`);
    const result = await this.hallOfFameService.getModuleHallOfFame(AwardModule.DIARY, limit);
    return ApiResponse.success(result, 'Diary Hall of Fame retrieved successfully');
  }

  @Get('diary/ongoing')
  @ApiOperation({
    summary: 'Get Ongoing Diary Awards',
    description: 'Get current period award winners for Diary module'
  })
  @ApiOkResponseWithType(OngoingAwardsResponseDto, 'Ongoing Diary awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'frequency',
    required: false,
    enum: [AwardFrequency.WEEKLY, AwardFrequency.MONTHLY, AwardFrequency.YEARLY],
    description: 'Filter by award frequency. Diary module supports: weekly, monthly, yearly (based on actual configured awards)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getDiaryOngoingAwards(
    @Query('frequency') frequency?: AwardFrequency
  ): Promise<ApiResponse<OngoingAwardsResponseDto>> {
    this.logger.log(`Getting ongoing Diary awards with frequency: ${frequency}`);
    const queryDto: OngoingAwardsQueryDto = {
      module: AwardModule.DIARY,
      frequency: frequency
    };
    const result = await this.hallOfFameService.getOngoingAwards(queryDto);
    return ApiResponse.success(result, 'Ongoing Diary awards retrieved successfully');
  }

  @Get('novel')
  @ApiOperation({
    summary: 'Get Novel Hall of Fame',
    description: 'Get Hall of Fame for Novel module with award types and their configured frequencies (monthly, yearly)'
  })
  @ApiOkResponseWithType(ModuleHallOfFameDto, 'Novel Hall of Fame retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of winners to return (default: 50)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getNovelHallOfFame(
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number
  ): Promise<ApiResponse<ModuleHallOfFameDto>> {
    this.logger.log(`Getting Novel Hall of Fame, limit: ${limit}`);
    const result = await this.hallOfFameService.getModuleHallOfFame(AwardModule.NOVEL, limit);
    return ApiResponse.success(result, 'Novel Hall of Fame retrieved successfully');
  }

  @Get('novel/ongoing')
  @ApiOperation({
    summary: 'Get Ongoing Novel Awards',
    description: 'Get current period award winners for Novel module'
  })
  @ApiOkResponseWithType(OngoingAwardsResponseDto, 'Ongoing Novel awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'frequency',
    required: false,
    enum: [AwardFrequency.MONTHLY, AwardFrequency.YEARLY],
    description: 'Filter by award frequency. Novel module supports: monthly, yearly (based on actual configured awards)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getNovelOngoingAwards(
    @Query('frequency') frequency?: AwardFrequency
  ): Promise<ApiResponse<OngoingAwardsResponseDto>> {
    this.logger.log(`Getting ongoing Novel awards with frequency: ${frequency}`);
    const queryDto: OngoingAwardsQueryDto = {
      module: AwardModule.NOVEL,
      frequency: frequency
    };
    const result = await this.hallOfFameService.getOngoingAwards(queryDto);
    return ApiResponse.success(result, 'Ongoing Novel awards retrieved successfully');
  }

  @Get('essay')
  @ApiOperation({
    summary: 'Get Essay Hall of Fame',
    description: 'Get Hall of Fame for Essay module with award types and their configured frequencies (monthly, yearly)'
  })
  @ApiOkResponseWithType(ModuleHallOfFameDto, 'Essay Hall of Fame retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of winners to return (default: 50)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getEssayHallOfFame(
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number
  ): Promise<ApiResponse<ModuleHallOfFameDto>> {
    this.logger.log(`Getting Essay Hall of Fame, limit: ${limit}`);
    const result = await this.hallOfFameService.getModuleHallOfFame(AwardModule.ESSAY, limit);
    return ApiResponse.success(result, 'Essay Hall of Fame retrieved successfully');
  }

  @Get('essay/ongoing')
  @ApiOperation({
    summary: 'Get Ongoing Essay Awards',
    description: 'Get current period award winners for Essay module'
  })
  @ApiOkResponseWithType(OngoingAwardsResponseDto, 'Ongoing Essay awards retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden')
  @ApiQuery({
    name: 'frequency',
    required: false,
    enum: [AwardFrequency.MONTHLY, AwardFrequency.YEARLY],
    description: 'Filter by award frequency. Essay module supports: monthly, yearly (based on actual configured awards)'
  })
  @Roles(UserType.ADMIN, UserType.TUTOR, UserType.STUDENT)
  async getEssayOngoingAwards(
    @Query('frequency') frequency?: AwardFrequency
  ): Promise<ApiResponse<OngoingAwardsResponseDto>> {
    this.logger.log(`Getting ongoing Essay awards with frequency: ${frequency}`);
    const queryDto: OngoingAwardsQueryDto = {
      module: AwardModule.ESSAY,
      frequency: frequency
    };
    const result = await this.hallOfFameService.getOngoingAwards(queryDto);
    return ApiResponse.success(result, 'Ongoing Essay awards retrieved successfully');
  }
}
