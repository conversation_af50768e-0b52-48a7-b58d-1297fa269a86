import {
  Injectable,
  NotFoundException,
  ConflictException,
  Logger,
  BadRequestException
} from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource, Not, In } from "typeorm";
import { QATaskSubmissions, QASubmissionStatus } from "../../database/entities/qa-task-submissions.entity";
import { QATaskSubmissionHistory } from "../../database/entities/qa-task-submission-history.entity";
import { CurrentUserService } from "../../common/services/current-user.service";
import { QATaskMissions } from "../../database/entities/qa-task-missions.entity";
import { NotificationHelperService } from '../../modules/notification/notification-helper.service';
import { NotificationType } from '../../database/entities/notification.entity';
import { DeeplinkService, DeeplinkType } from '../../common/utils/deeplink.service';
import { QAMissionTasks } from "src/database/entities/qa-mission-tasks.entity";
import { QATaskSubmissionMarking } from "src/database/entities/qa-task-submission-marking.entity";

@Injectable()
export class QASubmissionService {
  private readonly logger = new Logger(QASubmissionService.name);

  constructor(
    @InjectRepository(QATaskMissions)
    private readonly qaTaskMissionsRepository: Repository<QATaskMissions>,
    @InjectRepository(QAMissionTasks)
    private readonly qaMissionTasksRepository: Repository<QAMissionTasks>,
    @InjectRepository(QATaskSubmissions)
    private readonly qaTaskSubmissionsRepository: Repository<QATaskSubmissions>,
    @InjectRepository(QATaskSubmissionHistory)
    private readonly qaTaskSubmissionHistoryRepository: Repository<QATaskSubmissionHistory>,
    @InjectRepository(QATaskSubmissionMarking)
    private readonly qaTaskSubmissionMarkingRepository: Repository<QATaskSubmissionMarking>,
    private readonly currentUserService: CurrentUserService,
    private readonly dataSource: DataSource,
    private readonly notificationHelper: NotificationHelperService,
    private readonly deeplinkService: DeeplinkService
  ) {}

  private toQATaskSubmissionResponseDto(submission: QATaskSubmissions): any {
    return {
      id: submission.id,
      taskId: submission.taskId,
      status: submission.status,
      // content: submission.submissionHistory?.content || '',
      // metaData: submission.submissionHistory?.metaData || {},
      isActive: submission.isActive,
      task: {
        id: submission.task?.id,
        title: submission.task?.title,
        description: submission.task?.description,
        wordLimitMinimum: submission.task?.wordLimitMinimum,
        wordLimitMaximum: submission.task?.wordLimitMaximum,
        deadline: submission.task?.deadline,
        instructions: submission.task?.instructions,
        totalScore: submission.task?.totalScore,
        sequence: submission.task?.sequence
      },
      createdAt: submission.createdAt,
      updatedAt: submission.updatedAt
    };
  }

  /**
   * Get the tutor ID for a student
   * @param studentId The student ID
   * @returns The tutor ID or null if no tutor is assigned
   */
  private async getTutorIdForStudent(studentId: string): Promise<string | null> {
    try {
      // Query the database to find the tutor assigned to this student
      // This is a simplified implementation - you may need to adjust based on your actual data model
      const query = `
        SELECT tutor_id
        FROM student_tutor_mapping
        WHERE student_id = $1
        AND is_active = true
        LIMIT 1
      `;

      const result = await this.dataSource.query(query, [studentId]);

      if (result && result.length > 0) {
        return result[0].tutor_id;
      }

      return null;
    } catch (error) {
      this.logger.error(`Error getting tutor for student ${studentId}: ${error.message}`, error.stack);
      return null;
    }
  }

  async startTask(taskId: string, currentId: string): Promise<any> {
    const task = await this.qaMissionTasksRepository.findOne({
      where: { id: taskId, isActive: true },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${taskId} not found`);
    }

    const activeSubmission = await this.qaTaskSubmissionsRepository.findOne({
      where: {
        taskId: taskId,
        createdBy: currentId,
        isActive: true,
        status: In([QASubmissionStatus.DRAFT, QASubmissionStatus.SUBMITTED]),
      }
    });

    if (activeSubmission) {
      throw new ConflictException(`Task with ID ${taskId} already started`);
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Create new submission
      const submission = this.qaTaskSubmissionsRepository.create({
        taskId: taskId,
        status: QASubmissionStatus.DRAFT,
        isActive: true,
        createdBy: currentId
      });

      const savedSubmission = await queryRunner.manager.save(submission);

      // Create initial submission history
      const submissionHistory = this.qaTaskSubmissionHistoryRepository.create({
        submissionId: savedSubmission.id,
        content: '',
        metaData: {},
        createdBy: currentId,
        wordCount: 0,
        submissionDate: Date(),
        sequenceNumber: 1
      });

      const savedHistory = await queryRunner.manager.save(submissionHistory);

      savedSubmission.latestSubmissionId = savedHistory.id;

      await queryRunner.manager.save(savedSubmission);

      const qaTaskSubmission = await queryRunner.manager.findOne(QATaskSubmissions, {
        where: { id: savedSubmission.id },
        relations: ['task', 'submissionHistory']
      });

      await queryRunner.commitTransaction();
      return this.toQATaskSubmissionResponseDto(qaTaskSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to start task: ${error.message}`);
    }
    finally {
      await queryRunner.release();
    }
  }

  async autoSaveContent(qaSubmissionUpdate: any, userId): Promise<any> {
    const { submissionId, content, wordCount, metaData } = qaSubmissionUpdate;
    const currentUserId = userId;
    const submission = await this.qaTaskSubmissionsRepository.findOne({
      where: {
        id: submissionId,
        isActive: true,
        //status: QASubmissionStatus.DRAFT,
        createdBy: currentUserId,
      },
      relations: ['task']
    });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    if (submission.status === QASubmissionStatus.SUBMITTED) {
      throw new ConflictException('Your submission is pending for review. You will be able to update again once the review is complete.');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const lastHistory = await this.qaTaskSubmissionHistoryRepository.findOne({
        where: { submissionId: submission.id, createdBy: currentUserId },
        order: { sequenceNumber: 'DESC' },
      });
      const nextSequenceNumber = lastHistory ? lastHistory.sequenceNumber + 1 : 1;

      // Create new submission history
      const submissionHistory = this.qaTaskSubmissionHistoryRepository.create({
        submissionId: submission.id,
        content: content,
        metaData: metaData || {},
        wordCount: wordCount, // Simple word count
        createdBy: currentUserId,
        submissionDate: new Date(),
        sequenceNumber: nextSequenceNumber
      });

      const savedHistory = await queryRunner.manager.save(submissionHistory);

      // Update submission with latest history ID
      submission.latestSubmissionId = savedHistory.id;
      submission.totalRevisions = (submission.totalRevisions || 0) + 1;
      submission.firstRevisionProgress = submission.isFirstRevision ? Math.min((savedHistory.wordCount / submission.task.wordLimitMinimum) * 100, 100) : submission.firstRevisionProgress;
      submission.status = QASubmissionStatus.DRAFT; // Ensure status is set to DRAFT
      await queryRunner.manager.save(submission);

      const updatedSubmission = await queryRunner.manager.findOne(QATaskSubmissions, {
        where: { id: submission.id },
        relations: ['task', 'submissionHistory']
      });

      await queryRunner.commitTransaction();
        //return this.toQATaskSubmissionResponseDto(updatedSubmission);
        return {
          id: updatedSubmission.id,
          taskId: updatedSubmission.taskId,
          status: updatedSubmission.status,
          content: content,
          wordCount: wordCount, 
          // content: updatedSubmission.submissionHistory?.content || '',
          // metaData: updatedSubmission.submissionHistory?.metaData || {},
          isActive: updatedSubmission.isActive,
          task: {
            id: updatedSubmission.task?.id,
            title: updatedSubmission.task?.title,
            description: updatedSubmission.task?.description,
            wordLimitMinimum: updatedSubmission.task?.wordLimitMinimum,
            wordLimitMaximum: updatedSubmission.task?.wordLimitMaximum,
            deadline: updatedSubmission.task?.deadline,
            instructions: updatedSubmission.task?.instructions,
            totalScore: updatedSubmission.task?.totalScore,
            sequence: updatedSubmission.task?.sequence
          },
          createdAt: updatedSubmission.createdAt,
          updatedAt: updatedSubmission.updatedAt
        };
      } catch (error) {
        await queryRunner.rollbackTransaction();
        throw new ConflictException(`Failed to save content: ${error.message}`);
      } finally {
        await queryRunner.release();
      }
  }

  async submitQA(createQATaskSubmissionDto: any, userId): Promise<any> {
    const { taskId, content, wordCount, metaData } = createQATaskSubmissionDto;
    const currentUserId = userId;

    const submission = await this.qaTaskSubmissionsRepository.findOne({
      where: {
        taskId: taskId,
        isActive: true,
        createdBy: currentUserId,
        //status: Not(QASubmissionStatus.SUBMITTED)
      }, 
      relations: ['task']
    });

    if (!submission) {
      throw new NotFoundException('Active submission not found');
    }

    if (submission.status === QASubmissionStatus.SUBMITTED) {
      throw new ConflictException('Your submission is pending for review. You will be able to submit again once the review is complete.');
    }

    if (submission.task.wordLimitMinimum && wordCount < submission.task.wordLimitMinimum) {
      throw new ConflictException(
        `Word count (${wordCount}) must be at least ${submission.task.wordLimitMinimum}`
      );
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const lastHistory = await this.qaTaskSubmissionHistoryRepository.findOne({
        where: { submissionId: submission.id },
        order: { sequenceNumber: 'DESC' },
      });
      const nextSequenceNumber = lastHistory ? lastHistory.sequenceNumber + 1 : 1;

      // Create final submission history
      const submissionHistory = this.qaTaskSubmissionHistoryRepository.create({
        submissionId: submission.id,
        content: content,
        metaData: metaData || {},
        createdBy: currentUserId,
        wordCount: wordCount ? wordCount : 0, // Simple word count
        submissionDate: new Date(),
        sequenceNumber: nextSequenceNumber
      });

      const savedHistory = await queryRunner.manager.save(submissionHistory);

      // Update submission status and latest history
      submission.status = QASubmissionStatus.SUBMITTED;
      submission.latestSubmissionId = savedHistory.id;
      submission.currentRevision += 1;
      submission.totalRevisions = (submission.totalRevisions || 0) + 1;
      submission.lastSubmittedAt = new Date();
      submission.firstRevisionProgress = submission.isFirstRevision ? Math.min((savedHistory.wordCount / submission.task.wordLimitMinimum) * 100, 100) : submission.firstRevisionProgress;

      if(!submission.firstSubmittedAt) {
        submission.firstSubmittedAt = new Date();
      }

      await queryRunner.manager.save(submission);

      const finalSubmission = await queryRunner.manager.findOne(QATaskSubmissions, {
        where: { id: submission.id },
        relations: ['task', 'submissionHistory']
      });

      await queryRunner.commitTransaction();

      // Send notification to the assigned tutor
      try {
        const studentId = userId();
        const tutorId = await this.getTutorIdForStudent(studentId);

        if (tutorId) {
          // Generate deeplinks
          const webLink = this.deeplinkService.getWebLink(DeeplinkType.QA_SUBMISSION, {
            id: submission.id
          });

          const deepLink = this.deeplinkService.getDeepLink(DeeplinkType.QA_SUBMISSION, {
            id: submission.id
          });

          // Create HTML content for rich notifications
          const htmlContent = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #333;">New Q&A Submission</h2>
              <p>A student has submitted a new Q&A task for your review.</p>
              <p><strong>Task:</strong> ${submission.task?.title || 'Q&A Task'}</p>
              <div style="margin: 20px 0;">
                <a href="${webLink}" style="background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; display: inline-block;">View Submission</a>
              </div>
            </div>
          `;

          // Send notification
          await this.notificationHelper.notify(
            tutorId,
            NotificationType.QA_SUBMISSION,
            'New Q&A Submission',
            `A student has submitted a new Q&A task for your review.`,
            {
              relatedEntityId: submission.id,
              relatedEntityType: 'qa_submission',
              htmlContent: htmlContent,
              webLink: webLink,
              deepLink: deepLink,
              sendEmail: true,
              sendPush: true,
              sendInApp: true,
              sendMobile: true,
              sendSms: false,
              sendRealtime: false
            }
          );

          this.logger.log(`Sent notification to tutor ${tutorId} for submission ${submission.id}`);
        }
      } catch (notificationError) {
        // Log the error but don't fail the submission
        this.logger.error(`Error sending notification: ${notificationError.message}`, notificationError.stack);
      }

      return this.toQATaskSubmissionResponseDto(finalSubmission);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw new ConflictException(`Failed to submit QA: ${error.message}`);
    } finally {
      await queryRunner.release();
    }
  }

  async getActiveTask(userId): Promise<any> {
    const currentUserId = userId;
    let submissionHistoryObj: any = null; // Initialize submissionHistory to null
    const activeSubmission = await this.qaTaskSubmissionsRepository.findOne({
      where: {
        createdBy: currentUserId,
        isActive: true,
        status: In([QASubmissionStatus.DRAFT]),
      },
      relations: ['task', 'submissionHistory']
    });

    if (!activeSubmission) {
      return null;
    }
    
    // else{
    //   submissionHistoryObj = await this.qaTaskSubmissionHistoryRepository.findOne({
    //     where: { submissionId: activeSubmission.id },   
    //     order: { submissionDate: 'DESC', sequenceNumber: 'DESC' },
    //   });
    // }

    submissionHistoryObj = await this.qaTaskSubmissionHistoryRepository.findOne({
      where: { submissionId: activeSubmission.id },   
      order: { submissionDate: 'DESC', sequenceNumber: 'DESC' },
    });

    //return this.toQATaskSubmissionResponseDto(activeSubmission);
    return {
      id: activeSubmission.id,
      taskId: activeSubmission.taskId,
      status: activeSubmission.status,
      isActive: activeSubmission.isActive,
      createdAt: activeSubmission.createdAt,
      updatedAt: activeSubmission.updatedAt,

      task: activeSubmission.task
        ? {
            id: activeSubmission.task.id,
            title: activeSubmission.task.title,
            description: activeSubmission.task.description,
            wordLimitMinimum: activeSubmission.task.wordLimitMinimum,
            wordLimitMaximum: activeSubmission.task.wordLimitMaximum,
            deadline: activeSubmission.task.deadline,
            instructions: activeSubmission.task.instructions,
            totalScore: activeSubmission.task.totalScore,
            sequence: activeSubmission.task.sequence,
          }
        : null,

      submissionHistory: submissionHistoryObj
        ? {
            id: submissionHistoryObj.id,
            content: submissionHistoryObj.content || '',
            metaData: submissionHistoryObj.metaData || {},
            createdAt: submissionHistoryObj.createdAt,
            updatedAt: submissionHistoryObj.updatedAt,
          }
        : null,
    };
  }

  private toQATaskResponseDto(task: QAMissionTasks): any {
    return {
      id: task.id,
      title: task.title,
      description: task.description,
      wordLimitMinimum: task.wordLimitMinimum,
      wordLimitMaximum: task.wordLimitMaximum,
      deadline: task.deadline,
      sequence: task.sequence,
      totalScore: task.totalScore,
      instructions: task.instructions,
      isActive: task.isActive,
      missionId: task.missionId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt
    };
  }

  async findStudentTaskById(id: string, userId: string): Promise<any> {
    try {
      
      let submissionHistoryObj = null;
      let submissionMarkObj = null;

      const task = await this.qaMissionTasksRepository.findOne({        
        relations: ['mission', 'submissions'],
        where: { id, isActive: true, submissions: { createdBy: userId } }
      });

      if (task.submissions.length > 0) {
        submissionHistoryObj = await this.qaTaskSubmissionHistoryRepository.findOne({
          where: { submissionId: task.submissions[0].id, createdBy: userId },
          order: { submissionDate: 'DESC', sequenceNumber: 'DESC' },
        })

        submissionMarkObj = await this.qaTaskSubmissionMarkingRepository.findOne({
          where: { submissionId: task.submissions[0].id },
        })
      }

      if (!task) {
        throw new NotFoundException(`Q&A mission task not found`);
      }

      // return {
      //   id: task.id,
      //   title: task.title,
      //   description: task.description,
      //   wordLimitMinimum: task.wordLimitMinimum,
      //   wordLimitMaximum: task.wordLimitMaximum,
      //   deadline: task.deadline,
      //   sequence: task.sequence,
      //   totalScore: task.totalScore,
      //   instructions: task.instructions,
      //   isActive: task.isActive,
      //   missionId: task.missionId,
      //   createdAt: task.createdAt,
      //   updatedAt: task.updatedAt,
      //   submissions: task.submissions.map(submission => {
      //     return {
      //       id: submission.id,
      //       taskId: submission.taskId,
      //       status: submission.status,
      //       progress: submission.firstRevisionProgress,
      //       isActive: submission.isActive,
      //       submissionHistory: submissionHistoryObj ? {
      //         submissionHistoryId: submissionHistoryObj.id,  
      //         content: submissionHistoryObj.content,
      //         wordCount: submissionHistoryObj.wordCount,
      //         metaData: submissionHistoryObj.metaData,
      //         createdBy: submissionHistoryObj.createdBy,
      //         submissionDate: submissionHistoryObj.submissionDate,
      //         sequenceNumber: submissionHistoryObj.sequenceNumber                        
      //       } : null,
      //       submissionMark: submissionMarkObj ? {
      //         id: submissionMarkObj.id,
      //         submissionId: submissionMarkObj.submissionId,
      //         submissionHistoryId: submissionMarkObj.submissionHistoryId,
      //         Feedback: submissionMarkObj.submissionFeedback,
      //         score: submissionMarkObj.score,
      //         taskRemarks: submissionMarkObj.taskRemarks
      //       } : null,
      //     }
      //   })
      // };
    
    
      // Inside findStudentTaskById method
      
      return {
        id: task.id,
        title: task.title,
        description: task.description,
        wordLimitMinimum: task.wordLimitMinimum,
        wordLimitMaximum: task.wordLimitMaximum,
        deadline: task.deadline,
        sequence: task.sequence,
        totalScore: task.totalScore,
        instructions: task.instructions,
        isActive: task.isActive,
        missionId: task.missionId,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
        submission: task.submissions ? {
            id: task.submissions[0].id,
            taskId: task.submissions[0].taskId,
            status: task.submissions[0].status,
            progress: task.submissions[0].firstRevisionProgress,
            isActive: task.submissions[0].isActive,
            submissionHistory: submissionHistoryObj ? {
              submissionHistoryId: submissionHistoryObj.id,  
              content: submissionHistoryObj.content,
              wordCount: submissionHistoryObj.wordCount,
              metaData: submissionHistoryObj.metaData,
              createdBy: submissionHistoryObj.createdBy,
              submissionDate: submissionHistoryObj.submissionDate,
              sequenceNumber: submissionHistoryObj.sequenceNumber                        
            } : null,
            submissionMark: submissionMarkObj ? {
              id: submissionMarkObj.id,
              submissionId: submissionMarkObj.submissionId,
              submissionHistoryId: submissionMarkObj.submissionHistoryId,
              Feedback: submissionMarkObj.submissionFeedback,
              score: submissionMarkObj.score,
              taskRemarks: submissionMarkObj.taskRemarks
            } : null
        } : null
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch QA task: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to fetch QA task');
    }
  }
}
