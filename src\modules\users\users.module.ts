import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { ProfileViewController } from './profile-view.controller';
import { TutorEducationService } from './tutor-education.service';
import { TutorEducationController } from './tutor-education.controller';
import { AuthModule } from '../auth/auth.module';
import { JwtService } from '@nestjs/jwt';
import { User } from '../../database/entities/user.entity';
import { ProfilePicture } from '../../database/entities/profile-picture.entity';
import { Role } from '../../database/entities/role.entity';
import { UserRole } from '../../database/entities/user-role.entity';
import { UserPlan } from '../../database/entities/user-plan.entity';
import { Plan } from '../../database/entities/plan.entity';
import { StudentTutorMapping } from '../../database/entities/student-tutor-mapping.entity';
import { TutorEducation } from '../../database/entities/tutor-education.entity';
import { TutorApproval } from '../../database/entities/tutor-approval.entity';
import { CommonModule } from '../../common/common.module';
import { MulterModule } from '@nestjs/platform-express';
import { EmailModule } from '../email/email.module';

@Module({
  imports: [
    forwardRef(() => AuthModule),
    TypeOrmModule.forFeature([
      User,
      Role,
      UserRole,
      Plan,
      UserPlan,
      ProfilePicture,
      StudentTutorMapping,
      TutorEducation,
      TutorApproval
    ]),
    CommonModule,
    EmailModule,
    MulterModule.register({
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
  ],
  providers: [
    UsersService,
    JwtService,
    TutorEducationService
  ],
  controllers: [
    UsersController,
    ProfileViewController,
    TutorEducationController
  ],
  exports: [UsersService, TutorEducationService],
})
export class UsersModule {}
