import { Injectable, NotFoundException, ForbiddenException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { MissionDiaryEntryHistory } from '../../database/entities/mission-diary-entry-history.entity';
import { MissionDiaryEntry } from '../../database/entities/mission-diary-entry.entity';
import {
  MissionDiaryEntryHistoryResponseDto,
  MissionDiaryEntryVersionDto
} from '../../database/models/mission-diary-entry.dto';

@Injectable()
export class MissionDiaryEntryHistoryService {
  private readonly logger = new Logger(MissionDiaryEntryHistoryService.name);

  constructor(
    @InjectRepository(MissionDiaryEntryHistory)
    private readonly missionDiaryEntryHistoryRepository: Repository<MissionDiaryEntryHistory>,
    @InjectRepository(MissionDiaryEntry)
    private readonly missionDiaryEntryRepository: Repository<MissionDiaryEntry>,
    private readonly dataSource: DataSource
  ) {}

  /**
   * Create a new version during mission diary entry update
   */
  async createVersionFromUpdate(
    missionEntryId: string,
    oldData: { content: string },
    newData: { content?: string },
    userId: string,
    request?: any
  ): Promise<MissionDiaryEntryHistory> {
    try {
      // Get current entry to determine next version number
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: missionEntryId }
      });

      if (!entry) {
        throw new NotFoundException(`Mission diary entry with ID ${missionEntryId} not found`);
      }

      // Check ownership
      if (entry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to create versions for this mission entry');
      }

      // Mark all existing versions as not latest
      await this.missionDiaryEntryHistoryRepository.update(
        { missionEntryId: missionEntryId },
        { isLatest: false }
      );

      // Prepare new version data
      const newContent = newData.content !== undefined ? newData.content : oldData.content;

      // Calculate next version number based on existing history records
      const existingVersionsCount = await this.missionDiaryEntryHistoryRepository.count({
        where: { missionEntryId: missionEntryId }
      });
      const nextVersionNumber = existingVersionsCount + 1;

      // Create new version
      const newVersion = this.missionDiaryEntryHistoryRepository.create({
        missionEntryId: missionEntryId,
        content: newContent,
        versionNumber: nextVersionNumber,
        isLatest: true,
        wordCount: this.calculateWordCount(newContent),
        metaData: this.generateImplicitMetadata(request, oldData, { content: newContent }),
        createdBy: userId,
        updatedBy: userId
      });

      const savedVersion = await this.missionDiaryEntryHistoryRepository.save(newVersion);

      this.logger.log(`Created version ${nextVersionNumber} for mission entry ${missionEntryId}`);
      return savedVersion;
    } catch (error) {
      this.logger.error(`Error creating version for mission entry ${missionEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Calculate word count
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }

  /**
   * Generate metadata for version tracking
   */
  private generateImplicitMetadata(request: any, oldData: any, newData: any): any {
    const oldContent = oldData.content || '';
    const newContent = newData.content || '';

    return {
      ipAddress: request?.ip || request?.connection?.remoteAddress,
      userAgent: request?.headers?.['user-agent'],
      contentLength: newContent.length,
      contentLengthDiff: newContent.length - oldContent.length,
      significantChange: this.detectSignificantChange(oldContent, newContent),
      updateTrigger: this.detectUpdateTrigger(request),
      wordCountChange: this.calculateWordCount(newContent) - this.calculateWordCount(oldContent),
      hasNewParagraphs: this.detectNewParagraphs(oldContent, newContent),
      editDistance: this.calculateEditDistance(oldContent, newContent)
    };
  }

  /**
   * Detect if this is a significant change
   */
  private detectSignificantChange(oldContent: string, newContent: string): boolean {
    const oldWords = this.calculateWordCount(oldContent);
    const newWords = this.calculateWordCount(newContent);
    const wordDiff = Math.abs(newWords - oldWords);

    // Consider significant if word count changes by more than 10% or 5 words
    return wordDiff > Math.max(oldWords * 0.1, 5);
  }

  /**
   * Detect update trigger
   */
  private detectUpdateTrigger(request: any): string {
    const userAgent = request?.headers?.['user-agent'] || '';
    if (userAgent.includes('Mobile')) return 'mobile';
    if (userAgent.includes('Tablet')) return 'tablet';
    return 'desktop';
  }

  /**
   * Detect new paragraphs
   */
  private detectNewParagraphs(oldContent: string, newContent: string): boolean {
    const oldParagraphs = oldContent.split('\n').filter(p => p.trim().length > 0).length;
    const newParagraphs = newContent.split('\n').filter(p => p.trim().length > 0).length;
    return newParagraphs > oldParagraphs;
  }

  /**
   * Calculate edit distance (simplified Levenshtein distance)
   */
  private calculateEditDistance(oldContent: string, newContent: string): number {
    const oldLen = oldContent.length;
    const newLen = newContent.length;

    if (oldLen === 0) return newLen;
    if (newLen === 0) return oldLen;

    // Simplified calculation for performance
    const maxLen = Math.max(oldLen, newLen);
    const minLen = Math.min(oldLen, newLen);

    return maxLen - minLen;
  }

  /**
   * Get version history for a mission diary entry
   */
  async getVersionHistory(missionEntryId: string, userId: string): Promise<MissionDiaryEntryHistoryResponseDto> {
    try {
      // Verify ownership
      const entry = await this.missionDiaryEntryRepository.findOne({
        where: { id: missionEntryId, studentId: userId }
      });

      if (!entry) {
        throw new NotFoundException(`Mission diary entry with ID ${missionEntryId} not found`);
      }

      // Get all versions
      const versions = await this.missionDiaryEntryHistoryRepository.find({
        where: { missionEntryId },
        order: { versionNumber: 'DESC' }
      });

      return {
        missionEntryId,
        totalEditHistory: versions.length,
        currentVersionId: undefined, // Mission entries don't have currentVersionId
        versions: versions.map(version => this.mapVersionToDto(version))
      };
    } catch (error) {
      this.logger.error(`Error getting version history for mission entry ${missionEntryId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get a specific version by ID
   */
  async getVersion(versionId: string, userId: string): Promise<MissionDiaryEntryVersionDto> {
    try {
      const version = await this.missionDiaryEntryHistoryRepository.findOne({
        where: { id: versionId },
        relations: ['missionEntry']
      });

      if (!version) {
        throw new NotFoundException(`Version with ID ${versionId} not found`);
      }

      // Check ownership
      if (version.missionEntry.studentId !== userId) {
        throw new ForbiddenException('You do not have permission to view this version');
      }

      return this.mapVersionToDto(version);
    } catch (error) {
      this.logger.error(`Error getting version ${versionId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set a version as the latest (restore functionality)
   */
  async setLatestVersion(missionEntryId: string, versionId: string, userId: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Verify the entry exists and user has permission
      const entry = await queryRunner.manager.findOne(MissionDiaryEntry, {
        where: { id: missionEntryId, studentId: userId }
      });

      if (!entry) {
        throw new NotFoundException(`Mission diary entry with ID ${missionEntryId} not found`);
      }

      // Verify the version exists and belongs to this entry
      const versionToRestore = await queryRunner.manager.findOne(MissionDiaryEntryHistory, {
        where: { id: versionId, missionEntryId: missionEntryId }
      });

      if (!versionToRestore) {
        throw new NotFoundException(`Version with ID ${versionId} not found for this mission entry`);
      }

      // Mark all versions as not latest
      await queryRunner.manager.update(
        MissionDiaryEntryHistory,
        { missionEntryId: missionEntryId },
        { isLatest: false }
      );

      // Mark the selected version as latest
      await queryRunner.manager.update(
        MissionDiaryEntryHistory,
        { id: versionId },
        { isLatest: true }
      );

      // Update the main entry with the restored content
      await queryRunner.manager.update(
        MissionDiaryEntry,
        { id: missionEntryId },
        {
          content: versionToRestore.content,
          updatedBy: userId
        }
      );

      await queryRunner.commitTransaction();
      this.logger.log(`Restored version ${versionToRestore.versionNumber} for mission entry ${missionEntryId}`);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error restoring version ${versionId} for mission entry ${missionEntryId}: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Map version entity to DTO
   */
  private mapVersionToDto(version: MissionDiaryEntryHistory): MissionDiaryEntryVersionDto {
    return {
      id: version.id,
      missionEntryId: version.missionEntryId,
      content: version.content,
      versionNumber: version.versionNumber,
      isLatest: version.isLatest,
      wordCount: version.wordCount,
      metaData: version.metaData,
      createdAt: version.createdAt,
      createdBy: version.createdBy
    };
  }
}
