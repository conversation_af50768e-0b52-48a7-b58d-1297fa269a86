import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AwardsService } from './awards.service';
import { AwardsController } from './awards.controller';
import { Award } from '../../database/entities/award.entity';
import { AwardWinner } from '../../database/entities/award-winner.entity';
import { RewardPoint } from '../../database/entities/reward-point.entity';

import { User } from '../../database/entities/user.entity';
import { Notification } from '../../database/entities/notification.entity';
import { CommonModule } from '../../common/common.module';
import { JwtService } from '@nestjs/jwt';
import { AwardScheduler } from './award.scheduler';
import { AwardSchedulerController } from './award-scheduler.controller';
import { DiaryModule } from '../diary/diary.module';
import { EssayModule } from '../essay/essay.module';
import { NovelModule } from '../novel/novel.module';
import { EmailModule } from '../email/email.module';
import { AwardNotificationService } from './award-notification.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Award, AwardWinner, RewardPoint, User, Notification]),
    CommonModule,
    EmailModule,
    forwardRef(() => DiaryModule),
    forwardRef(() => EssayModule),
    forwardRef(() => NovelModule),
  ],
  controllers: [AwardsController, AwardSchedulerController],
  providers: [
    {
      provide: AwardsService,
      useClass: AwardsService,
    },
    {
      provide: AwardScheduler,
      useClass: AwardScheduler,
    },
    {
      provide: AwardNotificationService,
      useClass: AwardNotificationService,
    },
    JwtService,
  ],
  exports: [AwardsService, AwardScheduler, AwardNotificationService],
})
export class AwardsModule {}
