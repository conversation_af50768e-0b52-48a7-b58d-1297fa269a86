import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ShopService } from './shop.service';
import { ShopCategoryService } from './shop-category.service';
import { ShopFileService } from './shop-file.service';
import { ShopPurchaseService } from './shop-purchase.service';
import { ShopSkinService } from './shop-skin.service';
import { ShopItemService } from './shop-item.service';
import { ShopController } from './shop.controller';
import { AdminShopController } from './admin-shop.controller';
import { ShoppingCartController } from './shopping-cart.controller';
import { RewardPointsController } from './reward-points.controller';
import { StudentOwnedItemController } from './student-owned-item.controller';
import { RewardPointSettingController } from './reward-point-setting.controller';
import { ShoppingCartService } from './shopping-cart.service';
import { StudentOwnedItemService } from './student-owned-item.service';
import { RewardPointSettingService } from './reward-point-setting.service';
import { ShopCategory } from '../../database/entities/shop-category.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { ShopItemPurchase } from '../../database/entities/shop-item-purchase.entity';
import { User } from '../../database/entities/user.entity';
import { ShopSkinMapping } from '../../database/entities/shop-skin-mapping.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { ShopItemRegistry } from '../../database/entities/shop-item-registry.entity';
import { ShoppingCart } from '../../database/entities/shopping-cart.entity';
import { ShoppingCartItem } from '../../database/entities/shopping-cart-item.entity';
import { RewardPoint } from '../../database/entities/reward-point.entity';
import { StudentOwnedItem } from '../../database/entities/student-owned-item.entity';
import { RewardPointSetting } from '../../database/entities/reward-point-setting.entity';
import { CommonModule } from '../../common/common.module';
import { PromotionsModule } from '../promotions/promotions.module';
import { AwardsModule } from '../awards/awards.module';
import { DiaryModule } from '../diary/diary.module';
import { Promotion } from '../../database/entities/promotion.entity';
import { Plan } from '../../database/entities/plan.entity';
import { PlansModule } from '../plans/plans.module';
import { PaymentModule } from '../payment/payment.module';
import { SubscriptionFeatureGuard } from '../../common/guards/subscription-feature.guard';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ShopCategory,
      ShopItem,
      ShopItemPurchase,
      User,
      ShopSkinMapping,
      DiarySkin,
      StudentDiarySkin,
      ShopItemRegistry,
      ShoppingCart,
      ShoppingCartItem,
      RewardPoint,
      StudentOwnedItem,
      RewardPointSetting,
      Promotion, // Add Promotion entity
      Plan      // Add Plan entity
    ]),
    CommonModule,
    forwardRef(() => PromotionsModule),
    forwardRef(() => AwardsModule),    // Handle circular dependency
    forwardRef(() => DiaryModule),
    forwardRef(() => PlansModule),     // Add PlansModule with circular dependency handling
    forwardRef(() => PaymentModule),   // Add PaymentModule with circular dependency handling
  ],
  controllers: [
    ShopController,
    AdminShopController,
    ShoppingCartController,
    RewardPointsController,
    StudentOwnedItemController,
    RewardPointSettingController
  ],
  providers: [
    // Core services
    {
      provide: ShopService,
      useClass: ShopService,
    },
    // Category management
    {
      provide: ShopCategoryService,
      useClass: ShopCategoryService,
    },
    // File handling
    {
      provide: ShopFileService,
      useClass: ShopFileService,
    },
    // Purchase management
    {
      provide: ShopPurchaseService,
      useClass: ShopPurchaseService,
    },
    // Skin management
    {
      provide: ShopSkinService,
      useClass: ShopSkinService,
    },
    // Item management
    {
      provide: ShopItemService,
      useClass: ShopItemService,
    },
    // Cart management
    {
      provide: ShoppingCartService,
      useClass: ShoppingCartService,
    },
    // Student owned items
    {
      provide: StudentOwnedItemService,
      useClass: StudentOwnedItemService,
    },
    // Reward points
    {
      provide: RewardPointSettingService,
      useClass: RewardPointSettingService,
    },
    // Guards
    SubscriptionFeatureGuard
  ],
  exports: [
    ShopService,
    ShopCategoryService,
    ShopFileService,
    ShopPurchaseService,
    ShopSkinService,
    ShopItemService,
    ShoppingCartService,
    StudentOwnedItemService,
    RewardPointSettingService
  ],
})
export class ShopModule {}
