# KCP API Integration - Corrected Implementation

## Overview

Based on the KCP team's documentation, the correct integration uses the **KCP Payment API** endpoints:

- **Development**: `https://stg-spl.kcp.co.kr/gw/enc/v1/payment`
- **Production**: `https://spl.kcp.co.kr/gw/enc/v1/payment`

## Corrected Payment Flow

### 1. Payment Initiation API Call

When user initiates payment (checkout/subscribe), your backend calls KCP Payment API:

```http
POST https://stg-spl.kcp.co.kr/gw/enc/v1/payment
Content-Type: application/json

{
  "site_cd": "YOUR_SITE_CD",
  "kcp_cert_info": "YOUR_CERT_INFO",
  "tran_cd": "00100000",
  "tno": "TXN-1750141860437",
  "ordr_idxx": "ORDER-123",
  "good_name": "Product Name",
  "good_mny": 22.99,
  "buyr_name": "<PERSON>",
  "buyr_mail": "<EMAIL>",
  "buyr_tel1": "010-1234-5678",
  "pay_method": "100000000000",
  "currency": "KRW",
  "ordr_chk": "generated_hash",
  "kcp_sign_data": "generated_signature",
  "Ret_URL": "http://**************:3010/payment/kcp/redirect",
  "user_agent": "HEC-Frontend/1.0",
  "escw_used": "N",
  "tax_flag": "TG03",
  "good_expr": "2024-01-01 12:30:00"
}
```

### 2. KCP API Response

KCP returns a payment URL with token:

```json
{
  "res_cd": "0000",
  "res_msg": "SUCCESS",
  "payment_url": "https://stg-spl.kcp.co.kr/gw/payment.jsp?tno=TXN-1750141860437&token=PAYMENT_TOKEN_123",
  "token": "PAYMENT_TOKEN_123"
}
```

### 3. Your API Response to Frontend

Your checkout/subscribe API now returns the KCP payment URL:

```json
{
  "success": true,
  "data": {
    "paymentTransactionId": "TXN-1750141860437",
    "paymentUrl": "https://stg-spl.kcp.co.kr/gw/payment.jsp?tno=TXN-1750141860437&token=PAYMENT_TOKEN_123",
    "message": "Payment initiated successfully",
    "expiresAt": "2024-01-01T12:30:00Z"
  }
}
```

### 4. Frontend Redirects User

```javascript
// Frontend receives the payment URL and redirects
const response = await fetch('/shop/cart/checkout', { /* ... */ });
const data = await response.json();

// Now redirects to actual KCP payment page with token
window.location.href = data.paymentUrl;
```

### 5. User Completes Payment on KCP

User sees KCP's secure payment interface and completes payment.

### 6. KCP Redirects Back to Your Backend

```
http://**************:3010/payment/kcp/redirect?tno=TXN-1750141860437&ordr_idxx=ORDER-123&res_cd=0000&res_msg=SUCCESS
```

### 7. Your Backend Processes Result

Your redirect handler processes the result and redirects user to success/failure page.

## Implementation Details

### Updated KCP Service

```typescript
// src/modules/payment/services/kcp.service.ts

private async generatePaymentUrl(
  tradeRegResponse: KcpTradeRegResponse, 
  request: PaymentInitiationRequest
): Promise<string> {
  try {
    // Get the KCP payment API URL
    const kcpApiUrl = this.kcpConfig.getPaymentUrl();
    
    // Prepare payment request payload for KCP API
    const paymentPayload = {
      site_cd: this.kcpConfig.getSiteCd(),
      kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
      tran_cd: '00100000',
      tno: tradeRegResponse.tno,
      ordr_idxx: request.orderId,
      good_name: request.productName,
      good_mny: request.amount,
      buyr_name: request.buyerName,
      buyr_mail: request.buyerEmail,
      buyr_tel1: request.buyerPhone,
      pay_method: this.getPayMethodCode(request.paymentMethod),
      currency: request.currency || 'KRW',
      ordr_chk: tradeRegResponse.ordr_chk,
      kcp_sign_data: tradeRegResponse.kcp_sign_data,
      Ret_URL: `${this.configService.get('API_URL')}/payment/kcp/redirect`,
      user_agent: 'HEC-Frontend/1.0',
      escw_used: 'N',
      tax_flag: 'TG03',
      good_expr: new Date(Date.now() + 30 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')
    };

    // Make actual HTTP POST request to KCP API
    const response = await this.httpService.post(kcpApiUrl, paymentPayload).toPromise();
    
    if (response.data.res_cd !== '0000') {
      throw new Error(`KCP API error: ${response.data.res_msg}`);
    }

    return response.data.payment_url;
  } catch (error) {
    this.logger.error(`Failed to generate payment URL via KCP API: ${error.message}`);
    throw new Error('Failed to generate payment URL');
  }
}
```

### Configuration

```typescript
// src/modules/payment/services/kcp-config.service.ts

private loadConfig(): KcpConfig {
  return {
    siteCd: this.configService.get<string>('KCP_SITE_CD'),
    siteKey: this.configService.get<string>('KCP_SITE_KEY'),
    apiUrl: this.configService.get<string>('KCP_API_URL', 'https://stg-spl.kcp.co.kr'),
    tradeRegUrl: this.configService.get<string>('KCP_TRADE_REG_URL', '/std/tradeReg/register'),
    paymentUrl: this.configService.get<string>('KCP_PAYMENT_URL', '/gw/enc/v1/payment'), // Correct API endpoint
    webhookSecret: this.configService.get<string>('KCP_WEBHOOK_SECRET'),
    // ...
  };
}
```

## Environment Variables

```env
# KCP Configuration
KCP_SITE_CD=your_site_code
KCP_SITE_KEY=your_site_key
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_PAYMENT_URL=/gw/enc/v1/payment
KCP_WEBHOOK_SECRET=your_webhook_secret

# Your API Configuration
API_URL=http://**************:3010
FRONTEND_URL=https://your-frontend-domain.com
```

## Benefits of This Approach

1. **✅ Follows KCP's Official Documentation**: Uses the correct API endpoints
2. **✅ Secure Token-Based Flow**: KCP generates secure payment tokens
3. **✅ Better Error Handling**: API responses include detailed error information
4. **✅ Scalable**: Can handle high-volume transactions
5. **✅ Future-Proof**: Follows KCP's recommended integration pattern

## Testing

1. **Test API Integration**:
```bash
curl -X POST https://stg-spl.kcp.co.kr/gw/enc/v1/payment \
  -H "Content-Type: application/json" \
  -d '{ /* payment payload */ }'
```

2. **Test Your Checkout API**:
```bash
curl -X POST http://**************:3010/shop/cart/checkout \
  -H "Authorization: Bearer YOUR_JWT" \
  -d '{ /* checkout data */ }'
```

3. **Verify Payment URL**: The response should contain a KCP payment URL with token

## Next Steps

1. **Implement HTTP Client**: Add actual HTTP calls to KCP API (currently simulated)
2. **Add Error Handling**: Handle various KCP API error responses
3. **Add Logging**: Log all KCP API interactions for debugging
4. **Test Integration**: Test with KCP's staging environment
5. **Production Deployment**: Switch to production KCP endpoints

This implementation now correctly follows KCP's API-based integration pattern! 🎉
