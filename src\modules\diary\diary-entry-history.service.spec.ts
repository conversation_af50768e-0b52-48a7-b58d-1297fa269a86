import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { DiaryEntryHistoryService } from './diary-entry-history.service';
import { DiaryEntryHistory } from '../../database/entities/diary-entry-history.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import LoggerService from '../../common/services/logger.service';
import { NotFoundException, ForbiddenException } from '@nestjs/common';

describe('DiaryEntryHistoryService', () => {
  let service: DiaryEntryHistoryService;
  let historyRepository: Repository<DiaryEntryHistory>;
  let entryRepository: Repository<DiaryEntry>;
  let dataSource: DataSource;
  let logger: LoggerService;

  const mockHistoryRepository = {
    create: jest.fn(),
    save: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
  };

  const mockEntryRepository = {
    findOne: jest.fn(),
  };

  const mockDataSource = {
    createQueryRunner: jest.fn(() => ({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        update: jest.fn(),
      },
    })),
  };

  const mockLogger = {
    log: jest.fn(),
    error: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DiaryEntryHistoryService,
        {
          provide: getRepositoryToken(DiaryEntryHistory),
          useValue: mockHistoryRepository,
        },
        {
          provide: getRepositoryToken(DiaryEntry),
          useValue: mockEntryRepository,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: LoggerService,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<DiaryEntryHistoryService>(DiaryEntryHistoryService);
    historyRepository = module.get<Repository<DiaryEntryHistory>>(getRepositoryToken(DiaryEntryHistory));
    entryRepository = module.get<Repository<DiaryEntry>>(getRepositoryToken(DiaryEntry));
    dataSource = module.get<DataSource>(DataSource);
    logger = module.get<LoggerService>(LoggerService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createVersionFromUpdate', () => {
    it('should create a new version successfully', async () => {
      const diaryEntryId = 'entry-id';
      const userId = 'user-id';
      const oldData = { title: 'Old Title', content: 'Old content' };
      const newData = { title: 'New Title', content: 'New content' };

      const mockEntry = {
        id: diaryEntryId,
        diary: { userId },
        totalVersions: 1,
        currentVersionId: 'old-version-id',
      };

      const mockNewVersion = {
        id: 'new-version-id',
        diaryEntryId,
        title: 'New Title',
        content: 'New content',
        versionNumber: 2,
        isLatest: true,
        wordCount: 2,
      };

      mockEntryRepository.findOne.mockResolvedValue(mockEntry);
      mockHistoryRepository.update.mockResolvedValue({ affected: 1 });
      mockHistoryRepository.create.mockReturnValue(mockNewVersion);
      mockHistoryRepository.save.mockResolvedValue(mockNewVersion);

      const result = await service.createVersionFromUpdate(diaryEntryId, oldData, newData, userId);

      expect(result).toEqual(mockNewVersion);
      expect(mockHistoryRepository.update).toHaveBeenCalledWith('old-version-id', { isLatest: false });
      expect(mockHistoryRepository.create).toHaveBeenCalledWith({
        diaryEntryId,
        title: 'New Title',
        content: 'New content',
        versionNumber: 2,
        isLatest: true,
        wordCount: 2,
        metaData: expect.any(Object),
        createdBy: userId,
        updatedBy: userId,
      });
    });

    it('should throw NotFoundException when diary entry not found', async () => {
      mockEntryRepository.findOne.mockResolvedValue(null);

      await expect(
        service.createVersionFromUpdate('non-existent-id', { title: '', content: '' }, { title: '', content: '' }, 'user-id')
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException when user is not owner', async () => {
      const mockEntry = {
        id: 'entry-id',
        diary: { userId: 'other-user-id' },
      };

      mockEntryRepository.findOne.mockResolvedValue(mockEntry);

      await expect(
        service.createVersionFromUpdate('entry-id', { title: '', content: '' }, { title: '', content: '' }, 'user-id')
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('getVersionHistory', () => {
    it('should return version history successfully', async () => {
      const diaryEntryId = 'entry-id';
      const userId = 'user-id';

      const mockEntry = {
        id: diaryEntryId,
        diary: { userId },
        totalVersions: 2,
        currentVersionId: 'current-version-id',
      };

      const mockVersions = [
        {
          id: 'version-2',
          diaryEntryId,
          title: 'Title v2',
          content: 'Content v2',
          versionNumber: 2,
          isLatest: true,
          wordCount: 4,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'version-1',
          diaryEntryId,
          title: 'Title v1',
          content: 'Content v1',
          versionNumber: 1,
          isLatest: false,
          wordCount: 4,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      mockEntryRepository.findOne.mockResolvedValue(mockEntry);
      mockHistoryRepository.find.mockResolvedValue(mockVersions);

      const result = await service.getVersionHistory(diaryEntryId, userId);

      expect(result).toEqual({
        diaryEntryId,
        totalVersions: 2,
        currentVersionId: 'current-version-id',
        versions: expect.arrayContaining([
          expect.objectContaining({ versionNumber: 2, isLatest: true }),
          expect.objectContaining({ versionNumber: 1, isLatest: false }),
        ]),
      });
    });
  });

  describe('calculateWordCount', () => {
    it('should calculate word count correctly', () => {
      // Access private method through service instance
      const calculateWordCount = (service as any).calculateWordCount.bind(service);

      expect(calculateWordCount('Hello world')).toBe(2);
      expect(calculateWordCount('  Hello   world  ')).toBe(2);
      expect(calculateWordCount('')).toBe(0);
      expect(calculateWordCount('   ')).toBe(0);
      expect(calculateWordCount('Single')).toBe(1);
    });
  });

  describe('detectSignificantChange', () => {
    it('should detect significant changes correctly', () => {
      // Access private method through service instance
      const detectSignificantChange = (service as any).detectSignificantChange.bind(service);

      expect(detectSignificantChange('Hello', 'Hello world! This is a much longer text.')).toBe(true);
      expect(detectSignificantChange('Hello world', 'Hello world!')).toBe(false);
      expect(detectSignificantChange('', 'This is a completely new text with many words.')).toBe(true);
    });
  });
});
