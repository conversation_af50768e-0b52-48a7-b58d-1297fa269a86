# Payment Gateway Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [API Endpoints](#api-endpoints)
4. [Authentication & Security](#authentication--security)
5. [Payment Flow](#payment-flow)
6. [Error Handling](#error-handling)
7. [Configuration](#configuration)
8. [Monitoring & Logging](#monitoring--logging)

## Overview

The HEC backend system integrates with KCP (Korea Cyber Payment) gateway to process payments for:
- **Shop Item Purchases**: Physical and digital products
- **Plan Subscriptions**: Monthly/yearly subscription plans

### Supported Payment Methods
- **Credit/Debit Cards** (`card`)
- **Bank Transfer** (`bank`)
- **Mobile Payment** (`mobile`)

### Key Features
- ✅ Secure payment processing with HMAC verification
- ✅ Real-time payment status tracking
- ✅ Webhook-based payment confirmation
- ✅ Automatic retry mechanisms
- ✅ Comprehensive audit logging
- ✅ Refund processing support

## Architecture

### Module Structure
```
src/modules/payment/
├── payment.module.ts           # Main payment module
├── payment.controller.ts       # API endpoints
├── services/
│   ├── payment.service.ts      # Core payment logic
│   ├── kcp.service.ts         # KCP integration
│   └── kcp-config.service.ts  # Configuration management
├── dto/
│   └── payment.dto.ts         # Request/response DTOs
├── interfaces/
│   └── kcp.interface.ts       # KCP type definitions
└── entities/
    ├── payment-transaction.entity.ts
    └── payment-webhook.entity.ts
```

### Database Schema

#### PaymentTransaction Entity
```typescript
{
  id: string (UUID)
  transactionId: string (unique)
  orderId: string
  userId: string
  amount: decimal
  currency: string
  status: PaymentTransactionStatus
  paymentMethod: KcpPaymentMethod
  purchaseType: PurchaseType
  referenceId: string
  kcpData: JSON
  createdAt: Date
  updatedAt: Date
  completedAt: Date
  expiresAt: Date
}
```

#### PaymentWebhook Entity
```typescript
{
  id: string (UUID)
  transactionId: string
  webhookType: string
  payload: JSON
  signature: string
  processed: boolean
  retryCount: number
  lastRetryAt: Date
  createdAt: Date
}
```

## API Endpoints

### Payment Management

#### 1. Initiate Payment
```http
POST /payment/initiate
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "orderId": "ORD-20240101-001",
  "amount": 29900,
  "currency": "KRW",
  "productName": "Premium Plan Subscription",
  "paymentMethod": "card",
  "purchaseType": "plan",
  "referenceId": "plan-premium-monthly",
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

**Note:** Buyer information (name, email, phone) is automatically extracted from the authenticated user's profile.

**Response:**
```json
{
  "success": true,
  "data": {
    "paymentTransactionId": "TXN-1750141860417-DO2K3T",
    "paymentUrl": "http://**************:3010/payment/kcp/redirect?tno=TXN-1750141860437&ordr_idxx=ORDER-0b11ee95-4211-4fe2-9bf1-e37dc01f5ce5-1750141860369&amount=22.990000000000002&pay_method=100000000000&ordr_chk=e25630544eb66f59dbe7a273c83866fa828d431c00e395b0c9090e56fdf6dc5f&kcp_sign_data=SIGN-1750141860437",
    "expiresAt": "2024-01-01T12:30:00Z",
    "message": "Payment initiated successfully"
  },
  "message": "Payment initiated successfully"
}
```

#### 2. Get Payment Status
```http
GET /payment/status/:transactionId
Authorization: Bearer <JWT_TOKEN>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "transactionId": "TXN-20240101-001",
    "status": "completed",
    "amount": 29900,
    "currency": "KRW",
    "paymentMethod": "card",
    "completedAt": "2024-01-01T12:15:30Z"
  }
}
```

#### 3. Process Payment (Internal)
```http
POST /payment/process
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

#### 4. Handle KCP Webhook
```http
POST /payment/webhook/kcp
Content-Type: application/json
X-KCP-Signature: <HMAC_SIGNATURE>
```

#### 5. Handle KCP Redirect
```http
GET /payment/kcp/redirect
```

#### 6. Process Refund
```http
POST /payment/refund
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "transactionId": "TXN-20240101-001",
  "amount": 29900,
  "reason": "Customer requested refund"
}
```

#### 7. Get User Transactions
```http
GET /payment/transactions
Authorization: Bearer <JWT_TOKEN>
Query Parameters:
  - page: number (default: 1)
  - limit: number (default: 10)
  - status: PaymentTransactionStatus (optional)
```

### Enhanced Existing Endpoints

#### Shop Cart Checkout
```http
POST /shop/cart/checkout
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "paymentMethod": "card",
  "returnUrl": "https://app.example.com/payment/success",
  "cancelUrl": "https://app.example.com/payment/cancel"
}
```

#### Plan Subscription
```http
POST /plans/subscribe
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "planId": "plan-premium-monthly",
  "paymentMethod": "card",
  "returnUrl": "https://app.example.com/payment/success",
  "cancelUrl": "https://app.example.com/payment/cancel"
}
```

## Authentication & Security

### JWT Authentication
All payment endpoints (except webhooks and redirects) require JWT authentication:
```http
Authorization: Bearer <JWT_TOKEN>
```

### Webhook Security
KCP webhooks are secured using HMAC-SHA256 signatures:
```http
X-KCP-Signature: sha256=<HMAC_SIGNATURE>
```

### Data Encryption
- Sensitive payment data is encrypted at rest
- All API communications use HTTPS/TLS
- PCI DSS compliance for card data handling

## Payment Flow

### 1. Shop Item Purchase Flow
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant KCP
    participant Database

    User->>Frontend: Add items to cart
    User->>Frontend: Proceed to checkout
    Frontend->>Backend: POST /shop/cart/checkout
    Backend->>Database: Create temporary purchase
    Backend->>KCP: Register trade
    KCP-->>Backend: Return payment URL
    Backend-->>Frontend: Return payment URL
    Frontend->>User: Redirect to KCP
    User->>KCP: Complete payment
    KCP->>Backend: Webhook notification
    Backend->>Database: Update transaction status
    Backend->>Database: Finalize purchase
```

### 2. Plan Subscription Flow
```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant KCP
    participant Database

    User->>Frontend: Select plan
    Frontend->>Backend: POST /plans/subscribe
    Backend->>Database: Create temporary user plan
    Backend->>KCP: Register trade
    KCP-->>Backend: Return payment URL
    Backend-->>Frontend: Return payment URL
    Frontend->>User: Redirect to KCP
    User->>KCP: Complete payment
    KCP->>Backend: Webhook notification
    Backend->>Database: Update transaction status
    Backend->>Database: Activate user plan
```

## Error Handling

### HTTP Status Codes
- `200 OK` - Successful operation
- `201 Created` - Payment initiated successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Missing or invalid JWT token
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Transaction not found
- `409 Conflict` - Transaction already processed
- `500 Internal Server Error` - Server error

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "PAYMENT_FAILED",
    "message": "Payment processing failed",
    "details": "Insufficient funds"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common Error Codes
- `INVALID_PAYMENT_DATA` - Invalid payment request
- `PAYMENT_EXPIRED` - Payment session expired
- `PAYMENT_FAILED` - Payment processing failed
- `TRANSACTION_NOT_FOUND` - Transaction not found
- `ALREADY_PROCESSED` - Transaction already processed
- `REFUND_FAILED` - Refund processing failed

## Configuration

### Environment Variables
```env
# KCP Configuration
KCP_SITE_CD=your_site_code
KCP_SITE_KEY=your_site_key
KCP_API_URL=https://stg-spl.kcp.co.kr
KCP_WEBHOOK_SECRET=your_webhook_secret

# Payment Settings
PAYMENT_PROVIDER=kcp
PAYMENT_CURRENCY=KRW
PAYMENT_TIMEOUT=30000

# URLs
PAYMENT_SUCCESS_URL=https://app.example.com/payment/success
PAYMENT_CANCEL_URL=https://app.example.com/payment/cancel
PAYMENT_WEBHOOK_URL=https://api.example.com/payment/webhook/kcp
```

### KCP Configuration Service
The `KcpConfigService` manages all KCP-related configurations:
- Site credentials
- API endpoints
- Signature generation
- URL construction

## Monitoring & Logging

### Payment Transaction Logging
All payment operations are logged with:
- Transaction ID
- User ID
- Amount and currency
- Payment method
- Status changes
- Error details

### Health Check Endpoint
```http
GET /payment/health
```

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "kcpConnection": "connected",
    "databaseConnection": "connected",
    "lastSuccessfulPayment": "2024-01-01T12:00:00Z"
  }
}
```

### Metrics and Analytics
- Payment success/failure rates
- Average payment processing time
- Popular payment methods
- Revenue tracking
- Error rate monitoring

---

**Next:** See [API Testing Flow Guide](./PAYMENT_API_TESTING_GUIDE.md) for comprehensive testing procedures.
