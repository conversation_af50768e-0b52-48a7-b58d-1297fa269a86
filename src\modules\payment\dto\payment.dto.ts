import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsNumber, IsEnum, IsOptional, IsEmail, IsUUI<PERSON>, Min, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { KcpPaymentMethod, PurchaseType, PaymentTransactionStatus } from '../../../database/entities/payment-transaction.entity';

/**
 * DTO for initiating payment
 */
export class InitiatePaymentDto {
  @ApiProperty({
    example: 'ORD-20240101-001',
    description: 'Unique order identifier'
  })
  @IsNotEmpty()
  @IsString()
  orderId: string;

  @ApiProperty({
    example: 29900,
    description: 'Payment amount in KRW'
  })
  @IsNotEmpty()
  @IsNumber()
  @Min(100, { message: 'Amount must be at least 100 KRW' })
  @Type(() => Number)
  amount: number;

  @ApiProperty({
    example: 'KRW',
    description: 'Currency code',
    default: 'KRW'
  })
  @IsOptional()
  @IsString()
  currency?: string = 'KRW';

  @ApiProperty({
    example: 'Premium Plan Subscription',
    description: 'Product or service name'
  })
  @IsNotEmpty()
  @IsString()
  productName: string;

  @ApiProperty({
    example: 'card',
    description: 'KCP payment method',
    enum: KcpPaymentMethod
  })
  @IsNotEmpty()
  @IsEnum(KcpPaymentMethod)
  paymentMethod: KcpPaymentMethod;

  @ApiProperty({
    example: 'John Doe',
    description: 'Buyer name'
  })
  @IsNotEmpty()
  @IsString()
  buyerName: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Buyer email address'
  })
  @IsNotEmpty()
  @IsEmail()
  buyerEmail: string;

  @ApiProperty({
    example: '010-1234-5678',
    description: 'Buyer phone number'
  })
  @IsNotEmpty()
  @IsString()
  buyerPhone: string;

  @ApiProperty({
    example: 'shop_item',
    description: 'Type of purchase',
    enum: PurchaseType
  })
  @IsNotEmpty()
  @IsEnum(PurchaseType)
  purchaseType: PurchaseType;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'Reference ID (shop item ID or plan ID)'
  })
  @IsNotEmpty()
  @IsUUID()
  referenceId: string;

  @ApiProperty({
    example: 'https://example.com/payment/success',
    description: 'Return URL after successful payment'
  })
  @IsNotEmpty()
  @IsString()
  returnUrl: string;

  @ApiProperty({
    example: 'https://example.com/payment/cancel',
    description: 'Return URL after cancelled payment'
  })
  @IsNotEmpty()
  @IsString()
  cancelUrl: string;

  @ApiProperty({
    example: { cartId: 'cart-123', promoCode: 'SUMMER20' },
    description: 'Additional metadata',
    required: false
  })
  @IsOptional()
  @IsObject()
  metadata?: any;
}

/**
 * DTO for payment initiation response
 */
export class PaymentInitiationResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  transactionId: string;

  @ApiProperty({ required: false })
  paymentUrl?: string;

  @ApiProperty({ required: false })
  redirectUrl?: string;

  @ApiProperty()
  message: string;

  @ApiProperty({ required: false })
  errorCode?: string;

  @ApiProperty({ required: false })
  expiresAt?: Date;
}

/**
 * DTO for processing payment
 */
export class ProcessPaymentDto {
  @ApiProperty({
    example: 'TXN-20240101-001',
    description: 'Transaction ID'
  })
  @IsNotEmpty()
  @IsString()
  transactionId: string;

  @ApiProperty({
    example: 'encrypted_payment_data',
    description: 'Encrypted payment data from KCP'
  })
  @IsNotEmpty()
  @IsString()
  encData: string;

  @ApiProperty({
    example: 'payment_signature',
    description: 'Payment signature for verification'
  })
  @IsNotEmpty()
  @IsString()
  signature: string;

  @ApiProperty({
    example: { additionalData: 'value' },
    description: 'Additional KCP response data',
    required: false
  })
  @IsOptional()
  @IsObject()
  additionalData?: any;
}

/**
 * DTO for verifying KCP payment result
 */
export class VerifyKcpPaymentDto {
  @ApiProperty({
    example: 'TXN-1750146251644',
    description: 'KCP transaction number'
  })
  @IsNotEmpty()
  @IsString()
  tno: string;

  @ApiProperty({
    example: 'ORDER-090cabb4-053f-474f-9bfb-c32e2782c0d1-1750146251572',
    description: 'Order ID'
  })
  @IsNotEmpty()
  @IsString()
  ordr_idxx: string;

  @ApiProperty({
    example: '0000',
    description: 'KCP result code (0000 = success)'
  })
  @IsNotEmpty()
  @IsString()
  res_cd: string;

  @ApiProperty({
    example: 'SUCCESS',
    description: 'KCP result message'
  })
  @IsNotEmpty()
  @IsString()
  res_msg: string;

  @ApiProperty({
    example: 'encrypted_info_data',
    description: 'KCP encrypted info data',
    required: false
  })
  @IsOptional()
  @IsString()
  enc_info?: string;

  @ApiProperty({
    example: 'encrypted_payment_data',
    description: 'KCP encrypted payment data',
    required: false
  })
  @IsOptional()
  @IsString()
  enc_data?: string;

  @ApiProperty({
    example: '00100000',
    description: 'KCP transaction code',
    required: false
  })
  @IsOptional()
  @IsString()
  tran_cd?: string;
}

/**
 * DTO for payment status response
 */
export class PaymentStatusResponseDto {
  @ApiProperty()
  transactionId: string;

  @ApiProperty({ enum: PaymentTransactionStatus })
  status: PaymentTransactionStatus;

  @ApiProperty()
  amount: number;

  @ApiProperty()
  currency: string;

  @ApiProperty({ enum: KcpPaymentMethod })
  paymentMethod: KcpPaymentMethod;

  @ApiProperty({ required: false })
  completedAt?: Date;

  @ApiProperty({ required: false })
  errorMessage?: string;

  @ApiProperty({ required: false })
  kcpData?: any;
}

/**
 * DTO for KCP webhook payload (based on official KCP specification)
 */
export class WebhookPayloadDto {
  // Basic webhook fields (always present)
  @ApiProperty({ description: 'Site code' })
  @IsNotEmpty()
  @IsString()
  site_cd: string;

  @ApiProperty({ description: 'KCP transaction number' })
  @IsNotEmpty()
  @IsString()
  tno: string;

  @ApiProperty({ description: 'Order number' })
  @IsNotEmpty()
  @IsString()
  order_no: string;

  @ApiProperty({ description: 'Transaction type code (TX00 for virtual account deposit)' })
  @IsNotEmpty()
  @IsString()
  tx_cd: string;

  @ApiProperty({ description: 'Transaction completion time' })
  @IsNotEmpty()
  @IsString()
  tx_tm: string;

  // Virtual account deposit fields (tx_cd = TX00)
  @ApiProperty({ required: false, description: 'Orderer name' })
  @IsOptional()
  @IsString()
  ipgm_name?: string;

  @ApiProperty({ required: false, description: 'Depositor name' })
  @IsOptional()
  @IsString()
  remitter?: string;

  @ApiProperty({ required: false, description: 'Deposit amount' })
  @IsOptional()
  @IsString()
  ipgm_mnyx?: string;

  @ApiProperty({ required: false, description: 'Bank code' })
  @IsOptional()
  @IsString()
  bank_code?: string;

  @ApiProperty({ required: false, description: 'Virtual account number' })
  @IsOptional()
  @IsString()
  account?: string;

  @ApiProperty({ required: false, description: 'Processing type code (13 = deposit cancelled)' })
  @IsOptional()
  @IsString()
  op_cd?: string;

  @ApiProperty({ required: false, description: 'Notification ID' })
  @IsOptional()
  @IsString()
  noti_id?: string;

  @ApiProperty({ required: false, description: 'Cash receipt approval number' })
  @IsOptional()
  @IsString()
  cash_a_no?: string;

  @ApiProperty({ required: false, description: 'Cash receipt approval time' })
  @IsOptional()
  @IsString()
  cash_a_dt?: string;

  @ApiProperty({ required: false, description: 'Cash receipt transaction number' })
  @IsOptional()
  @IsString()
  cash_no?: string;

  // Legacy fields for backward compatibility
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  ordr_idxx?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  res_cd?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  res_msg?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  amount?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  good_name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  buyr_name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  buyr_mail?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  pay_method?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  app_time?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  app_no?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  card_cd?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  card_name?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  kcp_sign_data?: string;
}

/**
 * DTO for refund request
 */
export class RefundRequestDto {
  @ApiProperty({
    example: 'TXN-20240101-001',
    description: 'Transaction ID to refund'
  })
  @IsNotEmpty()
  @IsString()
  transactionId: string;

  @ApiProperty({
    example: 10000,
    description: 'Refund amount (optional, defaults to full amount)',
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Min(100)
  @Type(() => Number)
  amount?: number;

  @ApiProperty({
    example: 'Customer requested refund',
    description: 'Reason for refund'
  })
  @IsNotEmpty()
  @IsString()
  reason: string;
}

/**
 * DTO for refund response
 */
export class RefundResponseDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  transactionId: string;

  @ApiProperty()
  refundAmount: number;

  @ApiProperty()
  message: string;

  @ApiProperty({ required: false })
  refundId?: string;

  @ApiProperty({ required: false })
  errorCode?: string;
}

/**
 * DTO for payment transaction list item
 */
export class PaymentTransactionDto {
  @ApiProperty({ example: 'TXN-1234567890', description: 'Transaction ID' })
  transactionId: string;

  @ApiProperty({ example: 'ORDER-123-456', description: 'Order ID' })
  orderId: string;

  @ApiProperty({ example: 10000, description: 'Transaction amount' })
  amount: number;

  @ApiProperty({ example: 'KRW', description: 'Currency code' })
  currency: string;

  @ApiProperty({ example: 'kcp_card', description: 'Payment method used' })
  paymentMethod: string;

  @ApiProperty({ example: 'completed', description: 'Transaction status' })
  status: string;

  @ApiProperty({ example: 'shop_item', description: 'Purchase type' })
  purchaseType: string;

  @ApiProperty({ example: '2024-01-01T12:00:00Z', description: 'Transaction creation date' })
  createdAt: string;

  @ApiProperty({ example: '2024-01-01T12:05:00Z', description: 'Transaction completion date', required: false })
  completedAt?: string;
}

/**
 * DTO for payment health check response
 */
export class PaymentHealthResponseDto {
  @ApiProperty({ example: 'healthy', description: 'Service health status' })
  status: string;

  @ApiProperty({ example: '2024-01-01T12:00:00Z', description: 'Health check timestamp' })
  timestamp: string;
}

/**
 * DTO for KCP webhook response (must return result code as per KCP specification)
 */
export class WebhookResponseDto {
  @ApiProperty({
    example: '0000',
    description: 'Result code (0000 = success, other = retry up to 10 times)'
  })
  result: string;

  @ApiProperty({
    example: true,
    description: 'Whether webhook was processed successfully',
    required: false
  })
  success?: boolean;

  @ApiProperty({
    example: 'Webhook processed successfully',
    description: 'Processing message',
    required: false
  })
  message?: string;
}
