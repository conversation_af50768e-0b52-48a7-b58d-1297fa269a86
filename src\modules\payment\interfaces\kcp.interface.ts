/**
 * KCP Payment Method Enum
 */
export enum KcpPaymentMethod {
  CARD = 'card',
  BANK = 'bank',
  MOBILE = 'mobile',
  VACCT = 'vacct'
}

/**
 * Purchase Type Enum
 */
export enum PurchaseType {
  SHOP_ITEM = 'shop_item',
  PLAN = 'plan'
}

/**
 * KCP Trade Registration Request
 */
export interface KcpTradeRegRequest {
  site_cd: string;
  kcp_cert_info: string;
  ordr_idxx: string;
  good_name: string;
  good_mny: number;
  buyr_name: string;
  buyr_mail: string;
  buyr_tel1: string;
  buyr_tel2?: string;
  buyr_addr?: string;
  rcvr_name?: string;
  rcvr_tel1?: string;
  rcvr_tel2?: string;
  rcvr_addr?: string;
  rcvr_zipx?: string;
  user_agent?: string;
  remote_addr?: string;
  currency?: string;
  shop_user_id?: string;
  pay_method?: string;
  Ret_URL?: string;
  escw_used?: string;
  ActionResult?: string;
  van_code?: string;
}

/**
 * KCP Trade Registration Response
 */
export interface KcpTradeRegResponse {
  res_cd: string;
  res_msg: string;
  tno: string;
  amount: string;
  pnt_issue: string;
  trace_no: string;
  PayUrl: string;
  ordr_chk: string;
  kcp_sign_data: string;
}

/**
 * KCP Payment Request
 */
export interface KcpPaymentRequest {
  site_cd: string;
  kcp_cert_info: string;
  tran_cd: string;
  ordr_idxx: string;
  good_name: string;
  good_mny: number;
  buyr_name: string;
  buyr_mail: string;
  buyr_tel1: string;
  pay_method: string;
  Ret_URL: string;
  escw_used?: string;
  user_agent?: string;
  remote_addr?: string;
  currency?: string;
  shop_user_id?: string;
  quotaopt?: string;
  kcp_noint?: string;
  kcp_tax_flag?: string;
  kcp_tax_mny?: string;
  kcp_vat_mny?: string;
  kcp_free_mny?: string;
  kcp_show_card_list?: string;
  ActionResult?: string;
  van_code?: string;
  enc_info?: string;
  enc_data?: string;
  ordr_chk?: string;
  kcp_sign_data?: string;
}

/**
 * KCP Payment Response
 */
export interface KcpPaymentResponse {
  res_cd: string;
  res_msg: string;
  tno: string;
  amount: string;
  pnt_issue: string;
  trace_no: string;
  bank_name?: string;
  bank_code?: string;
  bk_mny?: string;
  bankname?: string;
  depositor?: string;
  account?: string;
  va_date?: string;
  card_cd?: string;
  card_name?: string;
  app_time?: string;
  app_no?: string;
  noinf?: string;
  quota?: string;
  partcanc_yn?: string;
  card_mny?: string;
  card_no?: string;
  issuer_cd?: string;
  issuer_nm?: string;
  acquirer_cd?: string;
  acquirer_nm?: string;
  install_period?: string;
  noint?: string;
  join_no?: string;
  join_nm?: string;
  cp_cd?: string;
  cp_nm?: string;
  hp_id?: string;
  commid?: string;
  mobile_no?: string;
  tk_van_code?: string;
  tk_app_no?: string;
  cash_yn?: string;
  cash_authno?: string;
  cash_tr_code?: string;
  cash_id_info?: string;
  cash_no?: string;
}

/**
 * KCP Webhook Payload
 */
export interface KcpWebhookPayload {
  site_cd: string;
  ordr_idxx: string;
  tno: string;
  good_name: string;
  good_mny: string;
  buyr_name: string;
  buyr_mail: string;
  buyr_tel1: string;
  pay_method: string;
  res_cd: string;
  res_msg: string;
  amount: string;
  pnt_issue: string;
  trace_no: string;
  app_time?: string;
  app_no?: string;
  card_cd?: string;
  card_name?: string;
  card_no?: string;
  noinf?: string;
  quota?: string;
  bank_name?: string;
  bank_code?: string;
  bk_mny?: string;
  bankname?: string;
  depositor?: string;
  account?: string;
  va_date?: string;
  cash_yn?: string;
  cash_authno?: string;
  cash_tr_code?: string;
  cash_id_info?: string;
  cash_no?: string;
  escw_yn?: string;
  ActionResult?: string;
  van_code?: string;
  kcp_sign_data?: string;
}

/**
 * KCP Configuration
 */
export interface KcpConfig {
  siteCd: string;
  siteKey: string;
  apiUrl: string;
  tradeRegUrl: string;
  paymentUrl: string;
  webhookSecret: string;
  timeout: number;
  retryAttempts: number;
  environment: 'development' | 'staging' | 'production';
}

/**
 * Payment initiation request
 */
export interface PaymentInitiationRequest {
  orderId: string;
  amount: number;
  currency: string;
  productName: string;
  buyerName: string;
  buyerEmail: string;
  buyerPhone: string;
  paymentMethod: string;
  returnUrl: string;
  cancelUrl: string;
  userId: string;
  purchaseType: 'shop_item' | 'plan';
  referenceId: string;
  metadata?: any;
}

/**
 * Payment initiation response
 */
export interface PaymentInitiationResponse {
  success: boolean;
  transactionId: string;
  paymentUrl?: string;
  redirectUrl?: string;
  message: string;
  errorCode?: string;
  expiresAt?: Date;
  kcpFormData?: KcpPaymentFormData;
}

export interface KcpPaymentFormData {
  site_cd: string;
  site_name: string;
  ordr_idxx: string;
  good_name: string;
  good_mny: string;
  buyr_name: string;
  buyr_tel2: string;
  buyr_mail: string;
  pay_method: string;
  quotaopt: string;
  res_cd: string;
  res_msg: string;
  enc_info: string;
  enc_data: string;
  tran_cd: string;
  return_url: string;
  action_url: string;
  tno: string;
  ordr_chk: string;
  kcp_sign_data: string;
}

/**
 * Payment status response
 */
export interface PaymentStatusResponse {
  transactionId: string;
  status: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  completedAt?: Date;
  errorMessage?: string;
  kcpData?: any;
}
