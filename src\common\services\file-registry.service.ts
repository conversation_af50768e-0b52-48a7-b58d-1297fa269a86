import { Injectable, Logger, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProfilePictureRegistry } from '../../database/entities/profile-picture-registry.entity';
import { ProfilePicture } from '../../database/entities/profile-picture.entity';
import { ShopItemRegistry } from '../../database/entities/shop-item-registry.entity';
import { ShopItem } from '../../database/entities/shop-item.entity';
import { DiarySkinRegistry } from '../../database/entities/diary-skin-registry.entity';
import { DiarySkin } from '../../database/entities/diary-skin.entity';
import { StudentDiarySkinRegistry } from '../../database/entities/student-diary-skin-registry.entity';
import { StudentDiarySkin } from '../../database/entities/student-diary-skin.entity';
import { DiaryCoverRegistry } from '../../database/entities/diary-cover-registry.entity';
import { Diary } from '../../database/entities/diary.entity';
import { DiaryQrRegistry } from '../../database/entities/diary-qr-registry.entity';
import { DiaryEntry } from '../../database/entities/diary-entry.entity';
import { ShopCategory } from '../../database/entities/shop-category.entity';
import { StoryMakerRegistry } from '../../database/entities/story-maker-registry.entity';
import { StoryMaker } from '../../database/entities/story-maker.entity';
import { MessageRegistry } from '../../database/entities/message-registry.entity';
import { FileUtilService } from './file-util.service';
import { StorageConfigService } from './storage-config.service';
import { S3StorageProvider } from '../providers/s3-storage.provider';
import { FileEntityType } from '../enums/file-entity-type.enum';
import { StorageProvider } from '../enums/storage.enum';
import { S3UploadOptions } from '../interfaces/storage-provider.interface';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class FileRegistryService {
  private readonly logger = new Logger(FileRegistryService.name);

  constructor(
    @InjectRepository(ProfilePictureRegistry)
    private readonly profilePictureRegistryRepository: Repository<ProfilePictureRegistry>,
    @InjectRepository(ProfilePicture)
    private readonly profilePictureRepository: Repository<ProfilePicture>,
    @InjectRepository(ShopItemRegistry)
    private readonly shopItemRegistryRepository: Repository<ShopItemRegistry>,
    @InjectRepository(ShopItem)
    private readonly shopItemRepository: Repository<ShopItem>,
    @InjectRepository(DiarySkinRegistry)
    private readonly diarySkinRegistryRepository: Repository<DiarySkinRegistry>,
    @InjectRepository(DiarySkin)
    private readonly diarySkinRepository: Repository<DiarySkin>,
    @InjectRepository(StudentDiarySkinRegistry)
    private readonly studentDiarySkinRegistryRepository: Repository<StudentDiarySkinRegistry>,
    @InjectRepository(StudentDiarySkin)
    private readonly studentDiarySkinRepository: Repository<StudentDiarySkin>,
    @InjectRepository(DiaryCoverRegistry)
    private readonly diaryCoverRegistryRepository: Repository<DiaryCoverRegistry>,
    @InjectRepository(Diary)
    private readonly diaryRepository: Repository<Diary>,
    @InjectRepository(DiaryQrRegistry)
    private readonly diaryQrRegistryRepository: Repository<DiaryQrRegistry>,
    @InjectRepository(DiaryEntry)
    private readonly diaryEntryRepository: Repository<DiaryEntry>,
    @InjectRepository(ShopCategory)
    private readonly shopCategoryRepository: Repository<ShopCategory>,
    @InjectRepository(StoryMakerRegistry)
    private readonly storyMakerRegistryRepository: Repository<StoryMakerRegistry>,
    @InjectRepository(StoryMaker)
    private readonly storyMakerRepository: Repository<StoryMaker>,
    @InjectRepository(MessageRegistry)
    private readonly messageRegistryRepository: Repository<MessageRegistry>,
    @Inject(FileUtilService)
    private readonly fileUtilService: FileUtilService,
    private readonly storageConfigService: StorageConfigService,
    private readonly s3StorageProvider: S3StorageProvider,
  ) {}

  /**
   * Register a file in the registry
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entityId Entity ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file (optional)
   * @returns The created registry entry
   */
  async registerFile(entityType: FileEntityType, entityId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, userId?: string, shareUrl?: string): Promise<any> {
    try {
      switch (entityType) {
        case FileEntityType.PROFILE_PICTURE:
          return await this.registerProfilePicture(userId, filePath, fileName, mimeType, fileSize);
        case FileEntityType.SHOP_ITEM:
          return await this.registerShopItemFile(entityId, filePath, fileName, mimeType, fileSize, userId);
        case FileEntityType.DIARY_SKIN:
          return await this.registerDiarySkinFile(entityId, filePath, fileName, mimeType, fileSize, userId);
        case FileEntityType.STUDENT_DIARY_SKIN:
          return await this.registerStudentDiarySkinFile(entityId, filePath, fileName, mimeType, fileSize, userId);
        case FileEntityType.DIARY_COVER:
          return await this.registerDiaryCoverFile(entityId, filePath, fileName, mimeType, fileSize, userId);
        case FileEntityType.DIARY_QR:
          return await this.registerDiaryQrFile(entityId, filePath, fileName, mimeType, fileSize, shareUrl);
        case FileEntityType.STORY_MAKER:
          return await this.registerStoryMakerFile(entityId, filePath, fileName, mimeType, fileSize);
        case FileEntityType.MESSAGE_ATTACHMENT:
          return await this.registerMessageAttachment(entityId, filePath, fileName, mimeType, fileSize, userId);
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }
    } catch (error) {
      this.logger.error(`Error registering file for ${entityType} with ID ${entityId}: ${error.message}`);
      throw error;
    }
  }

  //define a method to getfiles by registry id
  /**
   * Get a file from the registry by its ID
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entityId Registry ID
   * @returns The registry entry or null if not found
   */
  async getFileByRegistryId(entityType: FileEntityType, entityId: string): Promise<any> {
    try {
      this.logger.log(`Getting file by registry ID for ${entityType} with ID ${entityId}`);
      let result = null;

      switch (entityType) {
        case FileEntityType.PROFILE_PICTURE:
          result = await this.profilePictureRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['profilePicture'],
          });
          break;
        case FileEntityType.SHOP_ITEM:
          result = await this.shopItemRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['shopItem'],
          });
          break;
        case FileEntityType.DIARY_SKIN:
          result = await this.diarySkinRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['diarySkin'],
          });
          break;
        case FileEntityType.STUDENT_DIARY_SKIN:
          result = await this.studentDiarySkinRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['studentDiarySkin'],
          });
          break;
        case FileEntityType.DIARY_COVER:
          result = await this.diaryCoverRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['diary'],
          });
          break;
        case FileEntityType.DIARY_QR:
          result = await this.diaryQrRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['diaryEntry'],
          });
          break;
        case FileEntityType.STORY_MAKER:
          result = await this.storyMakerRegistryRepository.findOne({
            where: { id: entityId },
            relations: ['storyMaker'],
          });
          break;
        case FileEntityType.MESSAGE_ATTACHMENT:
          result = await this.messageRegistryRepository.findOne({
            where: { id: entityId },
          });
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      if (result) {
        this.logger.log(`Found registry entry by ID for ${entityType} with ID ${entityId}`);
        if (entityType === FileEntityType.DIARY_SKIN) {
          this.logger.log(`Diary skin registry details - filePath: ${result.filePath}, fileName: ${result.fileName}, updatedAt: ${result.updatedAt}`);
        }
      } else {
        this.logger.log(`No registry entry found by ID for ${entityType} with ID ${entityId}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error getting file by ID for ${entityType} with ID ${entityId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get a file from the registry by entity ID
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entityId Entity ID (not registry ID)
   * @returns The registry entry or null if not found
   */
  async getFile(entityType: FileEntityType, entityId: string): Promise<any> {
    try {
      this.logger.log(`Getting file by entity ID for ${entityType} with ID ${entityId}`);
      let result = null;

      switch (entityType) {
        case FileEntityType.PROFILE_PICTURE:
          result = await this.getProfilePicture(entityId);
          break;
        case FileEntityType.SHOP_ITEM:
          result = await this.getShopItemFile(entityId);
          break;
        case FileEntityType.DIARY_SKIN:
          result = await this.getDiarySkinFile(entityId);
          break;
        case FileEntityType.STUDENT_DIARY_SKIN:
          result = await this.getStudentDiarySkinFile(entityId);
          break;
        case FileEntityType.DIARY_COVER:
          result = await this.getDiaryCoverFile(entityId);
          break;
        case FileEntityType.DIARY_QR:
          result = await this.getDiaryQrFile(entityId);
          break;
        case FileEntityType.STORY_MAKER:
          result = await this.getStoryMakerFile(entityId);
          break;
        case FileEntityType.MESSAGE_ATTACHMENT:
          result = await this.getMessageAttachmentFile(entityId);
          break;
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }

      if (result) {
        this.logger.log(`Found registry entry by entity ID for ${entityType} with ID ${entityId}`);
      } else {
        this.logger.log(`No registry entry found by entity ID for ${entityType} with ID ${entityId}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error getting file for ${entityType} with ID ${entityId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Get the absolute file path for an entity
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entity The entity object
   * @returns The absolute file path
   */
  getAbsoluteFilePath(entityType: FileEntityType, entity: any): string {
    switch (entityType) {
      case FileEntityType.PROFILE_PICTURE:
        return this.fileUtilService.getAbsoluteFilePath(entity.FilePath);
      case FileEntityType.SHOP_ITEM:
        return this.fileUtilService.getAbsoluteFilePath(entity.FilePath);
      case FileEntityType.DIARY_SKIN:
        return this.fileUtilService.getAbsoluteFilePath(entity.PreviewImagePath);
      case FileEntityType.STUDENT_DIARY_SKIN:
        return this.fileUtilService.getAbsoluteFilePath(entity.filePath);
      case FileEntityType.DIARY_COVER:
        return this.fileUtilService.getAbsoluteFilePath(entity.filePath);
      case FileEntityType.DIARY_QR:
        return this.fileUtilService.getAbsoluteFilePath(entity.filePath);
      case FileEntityType.STORY_MAKER:
        return this.fileUtilService.getAbsoluteFilePath(entity.filePath);
      case FileEntityType.MESSAGE_ATTACHMENT:
        return this.fileUtilService.getAbsoluteFilePath(entity.filePath);
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  }

  /**
   * Get a URL for a file
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entityId Entity ID
   * @returns URL for accessing the file with cache-busting query parameter
   */
  async getFileUrl(entityType: FileEntityType, entityId: string): Promise<string> {
    // Check if we're using S3 storage
    if (this.storageConfigService.isS3Provider()) {
      return await this.getS3FileUrl(entityType, entityId);
    }

    // Use local storage URL generation
    const baseUrl = this.fileUtilService.getBaseUrl();
    // Add cache-busting timestamp query parameter
    const timestamp = Date.now();

    switch (entityType) {
      case FileEntityType.PROFILE_PICTURE:
        return `${baseUrl}/media/profile-pictures/${entityId}?v=${timestamp}`;
      case FileEntityType.SHOP_ITEM:
        return `${baseUrl}/media/shop-items/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_SKIN:
        return `${baseUrl}/media/diary-skins/${entityId}?v=${timestamp}`;
      case FileEntityType.STUDENT_DIARY_SKIN:
        return `${baseUrl}/media/diary-skins/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_COVER:
        return `${baseUrl}/media/diary-covers/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_QR:
        return `${baseUrl}/media/diary-qr/${entityId}?v=${timestamp}`;
      case FileEntityType.STORY_MAKER:
        return `${baseUrl}/media/story-maker/${entityId}?v=${timestamp}`;
      case FileEntityType.MESSAGE_ATTACHMENT:
        return `${baseUrl}/media/message-attachments/${entityId}?v=${timestamp}`;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  }

  /**
   * Get S3 file URL (presigned URL)
   */
  private async getS3FileUrl(entityType: FileEntityType, entityId: string): Promise<string> {
    try {
      // Get the registry entry to find the S3 storage key
      const registry = await this.getFile(entityType, entityId);

      if (!registry) {
        throw new Error(`No file found for ${entityType} with ID: ${entityId}`);
      }

      // Check if this file is stored in S3
      if (registry.storageProvider === StorageProvider.S3 && registry.storageKey) {
        // Generate presigned URL for S3 file
        return await this.s3StorageProvider.getPresignedUrl(registry.storageKey);
      }

      // If file is not in S3, fall back to local URL
      this.logger.warn(`File ${entityType}:${entityId} is not stored in S3, falling back to local URL`);
      return this.getLocalFileUrl(entityType, entityId);

    } catch (error) {
      this.logger.error(`Failed to get S3 URL for ${entityType}:${entityId}: ${error.message}`);
      // Fall back to local URL generation
      return this.getLocalFileUrl(entityType, entityId);
    }
  }

  /**
   * Get local file URL (for backward compatibility)
   */
  private getLocalFileUrl(entityType: FileEntityType, entityId: string): string {
    const baseUrl = this.fileUtilService.getBaseUrl();
    const timestamp = Date.now();

    switch (entityType) {
      case FileEntityType.PROFILE_PICTURE:
        return `${baseUrl}/media/profile-pictures/${entityId}?v=${timestamp}`;
      case FileEntityType.SHOP_ITEM:
        return `${baseUrl}/media/shop-items/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_SKIN:
        return `${baseUrl}/media/diary-skins/${entityId}?v=${timestamp}`;
      case FileEntityType.STUDENT_DIARY_SKIN:
        return `${baseUrl}/media/diary-skins/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_COVER:
        return `${baseUrl}/media/diary-covers/${entityId}?v=${timestamp}`;
      case FileEntityType.DIARY_QR:
        return `${baseUrl}/media/diary-qr/${entityId}?v=${timestamp}`;
      case FileEntityType.STORY_MAKER:
        return `${baseUrl}/media/story-maker/${entityId}?v=${timestamp}`;
      case FileEntityType.MESSAGE_ATTACHMENT:
        return `${baseUrl}/media/message-attachments/${entityId}?v=${timestamp}`;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }
  }

  /**
   * Delete a file from disk
   * @param filePath Path to the file to delete
   */
  deleteFile(filePath: string): void {
    try {
      this.fileUtilService.deleteFile(filePath);
      this.logger.log(`Deleted file from disk: ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to delete file from disk: ${filePath}. Error: ${error.message}`);
    }
  }

  /**
   * Get a file URL with automatic fallback based on entity type and ID
   * @param entityType Type of entity
   * @param entityId ID of the entity (can be either registry ID or entity ID)
   * @returns URL for the file or null if no file exists
   */
  async getFileUrlWithFallback(entityType: FileEntityType, entityId: string): Promise<string | null> {
    if (!entityId) return null;

    this.logger.log(`Getting file URL for ${entityType} with ID: ${entityId}`);

    // For diary covers, we always want to use the diary ID in the URL
    if (entityType === FileEntityType.DIARY_COVER) {
      // First try to get the registry entry by diary ID
      let registry = await this.getFile(entityType, entityId);

      // If not found by diary ID, try by registry ID
      if (!registry) {
        this.logger.log(`Registry not found by diary ID, trying by registry ID: ${entityId}`);
        registry = await this.getFileByRegistryId(entityType, entityId);
      }

      // If we found a registry entry, generate URL using the diary ID
      if (registry && (registry.diaryId || registry.id)) {
        const diaryId = registry.diaryId || entityId;
        const url = this.getFileUrl(entityType, diaryId);
        this.logger.log(`Generated diary cover URL using diary ID: ${url}`);
        return url;
      }

      this.logger.warn(`No diary cover registry found for ID: ${entityId}`);
      return null;
    }

    // For other entity types, use the original logic
    // First try to get the registry entry by registry ID
    let registry = await this.getFileByRegistryId(entityType, entityId);

    // If not found by registry ID, try by entity ID
    if (!registry) {
      this.logger.log(`Registry not found by ID, trying by entity ID: ${entityId}`);
      registry = await this.getFile(entityType, entityId);
    }

    // If we found a registry entry with an ID, use that for the URL
    if (registry && registry.id) {
      const url = this.getFileUrl(entityType, registry.id);
      this.logger.log(`Generated URL using registry ID: ${url}`);
      return url;
    }

    // If we found a registry-like object without an ID (from our fallback methods)
    // but with a file path, we need to create a URL for it
    if (registry && (registry.filePath || registry.FilePath)) {
      // Get the registry ID for this entity if it exists
      const registryId = await this.getFileRegistryId(entityType, entityId);

      if (registryId) {
        // If we found a registry ID, use that for the URL
        const url = this.getFileUrl(entityType, registryId);
        this.logger.log(`Generated URL using found registry ID: ${url}`);
        return url;
      }

      // If we still don't have a registry ID, use the entity ID directly
      const url = this.getFileUrl(entityType, entityId);
      this.logger.log(`Generated URL using entity ID directly: ${url}`);
      return url;
    }

    this.logger.warn(`No file found for ${entityType} with ID: ${entityId}`);
    return null;
  }

  /**
   * Get a file registry entry ID for an entity
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param entityId Entity ID
   * @returns Registry entry ID or null if not found
   */
  async getFileRegistryId(entityType: FileEntityType, entityId: string): Promise<string | null> {
    try {
      switch (entityType) {
        case FileEntityType.PROFILE_PICTURE: {
          const registry = await this.profilePictureRegistryRepository.findOne({
            where: { profilePictureId: entityId },
            select: { id: true },
          });
          return registry?.id || null;
        }
        case FileEntityType.SHOP_ITEM: {
          const registry = await this.shopItemRegistryRepository.findOne({
            where: { shopItemId: entityId },
            select: { id: true },
          });
          return registry?.id || null;
        }
        case FileEntityType.DIARY_SKIN: {
          const registry = await this.diarySkinRegistryRepository.findOne({
            where: { diarySkinId: entityId },
            select: { id: true },
            order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
          });
          return registry?.id || null;
        }
        case FileEntityType.STUDENT_DIARY_SKIN: {
          const registry = await this.studentDiarySkinRegistryRepository.findOne({
            where: { studentDiarySkinId: entityId },
            select: { id: true },
            order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
          });
          return registry?.id || null;
        }
        case FileEntityType.DIARY_COVER: {
          const registry = await this.diaryCoverRegistryRepository.findOne({
            where: { diaryId: entityId },
            select: { id: true },
            order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
          });
          return registry?.id || null;
        }
        case FileEntityType.DIARY_QR: {
          const registry = await this.diaryQrRegistryRepository.findOne({
            where: { diaryEntryId: entityId },
            select: { id: true },
            order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
          });
          return registry?.id || null;
        }
        case FileEntityType.STORY_MAKER: {
          const registry = await this.storyMakerRegistryRepository.findOne({
            where: { storyMakerId: entityId },
            select: { id: true },
            order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
          });
          return registry?.id || null;
        }
        case FileEntityType.MESSAGE_ATTACHMENT: {
          const registry = await this.messageRegistryRepository.findOne({
            where: { id: entityId },
            select: { id: true },
          });
          return registry?.id || null;
        }
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }
    } catch (error) {
      this.logger.error(`Error getting file registry ID for ${entityType} with ID ${entityId}: ${error.message}`);
      return null;
    }
  }

  /**
   * Check if S3 storage provider is configured
   * @returns True if S3 provider is configured
   */
  isS3Provider(): boolean {
    return this.storageConfigService.isS3Provider();
  }

  /**
   * Get S3 file stream for direct serving
   * @param storageKey S3 storage key
   * @returns Readable stream of the file
   */
  async getS3FileStream(storageKey: string): Promise<any> {
    try {
      return await this.s3StorageProvider.getFileStream(storageKey);
    } catch (error) {
      this.logger.error(`Error getting S3 file stream for key ${storageKey}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload a file
   * @param entityType Type of entity (profile picture, shop item, diary skin)
   * @param file File to upload
   * @param referenceId Reference ID (userId for profile pictures, itemNumber for shop items, skinName for diary skins)
   * @param options Additional options for the upload
   * @returns Path to the uploaded file and the registry entry
   */
  async uploadFile(entityType: FileEntityType, file: any, referenceId: string, options?: any): Promise<{ filePath: string; registry: any }> {
    try {
      // Extract entityId from options if provided (for updates)
      const entityId = options?.entityId;

      this.logger.log(`Uploading file for ${entityType} with reference ID ${referenceId}${entityId ? ` and entity ID ${entityId}` : ''}`);

      // Check if we're using S3 storage
      if (this.storageConfigService.isS3Provider()) {
        return await this.uploadFileToS3(entityType, file, referenceId, options);
      }

      // Use local storage upload
      switch (entityType) {
        case FileEntityType.PROFILE_PICTURE:
          return await this.uploadProfilePicture(file, referenceId, entityId);
        case FileEntityType.SHOP_ITEM:
          return await this.uploadShopItemFile(file, referenceId, options?.categoryId, options?.userId, entityId);
        case FileEntityType.DIARY_SKIN:
          return await this.uploadDiarySkinFile(file, referenceId, options?.isStudentSkin, options?.userId, entityId);
        case FileEntityType.STUDENT_DIARY_SKIN:
          return await this.uploadStudentDiarySkinFile(file, referenceId, options?.userId, entityId);
        case FileEntityType.DIARY_COVER:
          return await this.uploadDiaryCoverFile(file, referenceId, options?.userId, entityId);
        case FileEntityType.DIARY_QR:
          return await this.uploadDiaryQrFile(file, referenceId, options?.shareUrl, entityId);
        case FileEntityType.STORY_MAKER:
          return await this.uploadStoryMakerFile(file, referenceId, entityId);
        case FileEntityType.MESSAGE_ATTACHMENT:
          return await this.uploadMessageAttachment(file, referenceId, options);
        default:
          throw new Error(`Unsupported entity type: ${entityType}`);
      }
    } catch (error) {
      this.logger.error(`Error uploading file for ${entityType} with reference ID ${referenceId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload file to S3 storage
   */
  private async uploadFileToS3(entityType: FileEntityType, file: any, referenceId: string, options?: any): Promise<{ filePath: string; registry: any }> {
    try {
      // Generate storage key for S3
      const storageKey = this.generateS3StorageKey(entityType, referenceId, file.originalname);

      // Prepare S3 upload options
      const s3Options: S3UploadOptions = {
        contentType: file.mimetype,
        metadata: {
          entityType,
          referenceId,
          entityId: options?.entityId || '',
          originalName: file.originalname,
          uploadedAt: new Date().toISOString()
        }
      };

      // Upload to S3
      const uploadResult = await this.s3StorageProvider.uploadFile(file, storageKey, s3Options);

      // Register in database with S3 metadata
      // For S3 uploads, entityId should always be provided to ensure proper UUID usage
      const entityId = options?.entityId;
      if (!entityId) {
        // For diary skins, entityId is critical - log as error
        if (entityType === FileEntityType.DIARY_SKIN) {
          this.logger.error(`EntityId is required for diary skin S3 uploads but was not provided. ReferenceId: ${referenceId}`);
        } else {
          this.logger.warn(`EntityId not provided for S3 upload of ${entityType}, falling back to referenceId: ${referenceId}`);
        }
      }

      const registry = await this.registerFileWithS3Metadata(
        entityType,
        entityId || referenceId, // Keep fallback for backward compatibility but log warning
        uploadResult,
        file,
        options
      );

      this.logger.log(`S3 upload completed for ${entityType}: ${storageKey}`);

      return {
        filePath: uploadResult.key,
        registry
      };

    } catch (error) {
      this.logger.error(`S3 upload failed for ${entityType}: ${error.message}`);
      throw new Error(`S3 upload failed: ${error.message}`);
    }
  }

  /**
   * Generate S3 storage key
   */
  private generateS3StorageKey(entityType: FileEntityType, referenceId: string, originalName: string): string {
    const timestamp = Date.now();
    const extension = originalName.split('.').pop();
    const sanitizedReferenceId = referenceId.replace(/[^a-zA-Z0-9]/g, '-');

    const typePrefix = entityType.replace('_', '-');
    return `${typePrefix}/${sanitizedReferenceId}/${timestamp}.${extension}`;
  }

  /**
   * Register file with S3 metadata
   */
  private async registerFileWithS3Metadata(
    entityType: FileEntityType,
    entityId: string,
    uploadResult: any,
    file: any,
    options?: any
  ): Promise<any> {
    // Create registry entry based on entity type
    let registry: any;

    switch (entityType) {
      case FileEntityType.PROFILE_PICTURE:
        registry = await this.registerProfilePictureWithS3(entityId, uploadResult, file);
        break;
      case FileEntityType.SHOP_ITEM:
        registry = await this.registerShopItemWithS3(entityId, uploadResult, file, options);
        break;
      case FileEntityType.DIARY_SKIN:
        registry = await this.registerDiarySkinWithS3(entityId, uploadResult, file, options);
        break;
      case FileEntityType.STUDENT_DIARY_SKIN:
        registry = await this.registerStudentDiarySkinWithS3(entityId, uploadResult, file, options);
        break;
      case FileEntityType.DIARY_COVER:
        registry = await this.registerDiaryCoverWithS3(entityId, uploadResult, file, options);
        break;
      case FileEntityType.DIARY_QR:
        registry = await this.registerDiaryQrWithS3(entityId, uploadResult, file, options);
        break;
      case FileEntityType.STORY_MAKER:
        registry = await this.registerStoryMakerWithS3(entityId, uploadResult, file);
        break;
      case FileEntityType.MESSAGE_ATTACHMENT:
        registry = await this.registerMessageAttachmentWithS3(entityId, uploadResult, file, options);
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    return registry;
  }

  // Private methods for specific entity types

  /**
   * Register a story maker file in the registry
   * @param storyMakerId Story maker ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @returns The created registry entry
   */
  private async registerStoryMakerFile(storyMakerId: string, filePath: string, fileName: string, mimeType: string, fileSize: number): Promise<StoryMakerRegistry> {
    try {
      let registry = await this.storyMakerRegistryRepository.findOne({
        where: { storyMakerId: storyMakerId },
      });

      // If an existing registry entry is found, delete the old file
      if (registry && registry.filePath) {
        this.deleteFile(registry.filePath);
      }

      if (!registry) {
        registry = this.storyMakerRegistryRepository.create({
          storyMakerId: storyMakerId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
      }

      return await this.storyMakerRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering story maker file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a story maker file from the registry
   * @param storyMakerId Story maker ID
   * @returns The registry entry or null if not found
   */
  private async getStoryMakerFile(storyMakerId: string): Promise<StoryMakerRegistry> {
    try {
      const registry = await this.storyMakerRegistryRepository.findOne({
        where: { storyMakerId: storyMakerId },
        relations: ['storyMaker'],
      });

      if (registry) {
        return registry;
      }

      // If no registry entry exists, try to get the story maker directly
      const storyMaker = await this.storyMakerRepository.findOne({
        where: { id: storyMakerId },
      });

      if (storyMaker && storyMaker.picture) {
        // Create a registry-like object with the file path
        return {
          id: null,
          storyMakerId: storyMaker.id,
          filePath: storyMaker.picture,
          fileName: path.basename(storyMaker.picture),
          mimeType: null,
          fileSize: null,
        } as StoryMakerRegistry;
      }

      this.logger.warn(`No story maker registry entry found for ID: ${storyMakerId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting story maker file: ${error.message}`);
      return null;
    }
  }

  /**
   * Upload a story maker file
   * @param file The file to upload
   * @param storyTitle The title of the story (for reference)
   * @param entityId Optional entity ID for updates
   * @returns The uploaded file path and registry entry
   */
  private async uploadStoryMakerFile(file: any, storyTitle: string, entityId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      // Create the story maker directory if it doesn't exist
      const storyMakerDir = path.join(this.fileUtilService.getUploadDir(), 'story-maker');
      if (!fs.existsSync(storyMakerDir)) {
        fs.mkdirSync(storyMakerDir, { recursive: true });
      }

      this.fileUtilService.validateFile(file, {
        maxSizeInMB: 2, // 2MB max size
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
      });

      // Generate a unique filename
      const sanitizedTitle = storyTitle.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
      const fileName = this.fileUtilService.generateUniqueFilename(file.originalname, `story-${sanitizedTitle}`);

      // Write the file to disk
      const filePath = this.fileUtilService.writeFileToDisk(file, 'story-maker', fileName);

      // If entityId is provided, register the file
      let registry = null;
      if (entityId) {
        registry = await this.registerStoryMakerFile(entityId, filePath, fileName, file.mimetype, file.size);
      }

      return {
        filePath,
        registry: registry || {
          filePath: filePath,
          fileName: file.originalname || fileName,
          mimeType: file.mimetype || 'application/octet-stream',
          fileSize: file.size || 0,
        },
      };
    } catch (error) {
      this.logger.error(`Error uploading story maker file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a diary QR code in the registry
   * @param diaryEntryId Diary entry ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param shareUrl Share URL for the diary entry
   * @returns The created registry entry
   */
  private async registerDiaryQrFile(diaryEntryId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, shareUrl: string): Promise<DiaryQrRegistry> {
    try {
      this.logger.log(`Registering diary QR file for entry ID ${diaryEntryId}`);

      // Check if a registry entry already exists
      let registry = await this.diaryQrRegistryRepository.findOne({
        where: { diaryEntryId: diaryEntryId },
      });

      // If an existing registry entry is found, delete the old file
      if (registry && registry.filePath) {
        this.deleteFile(registry.filePath);
      }

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.diaryQrRegistryRepository.create({
          diaryEntryId: diaryEntryId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          shareUrl: shareUrl,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        registry.shareUrl = shareUrl;
      }

      // Save the registry entry
      return await this.diaryQrRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering diary QR file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a diary QR file from the registry
   * @param diaryEntryId Diary entry ID
   * @returns The registry entry or null if not found
   */
  private async getDiaryQrFile(diaryEntryId: string): Promise<DiaryQrRegistry> {
    try {
      this.logger.log(`Getting diary QR file for entry ID ${diaryEntryId}`);

      // Check if a registry entry exists
      const registry = await this.diaryQrRegistryRepository.findOne({
        where: { diaryEntryId: diaryEntryId },
        relations: ['diaryEntry'],
      });

      if (registry) {
        this.logger.log(`Found registry entry for diary QR with entry ID: ${diaryEntryId}`);
        return registry;
      }

      this.logger.warn(`No diary QR registry entry found for entry ID: ${diaryEntryId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting diary QR file: ${error.message}`);
      return null;
    }
  }

  /**
   * Upload a diary QR file
   * @param file The file to upload (Buffer or File object)
   * @param diaryEntryId The diary entry ID
   * @param shareUrl The share URL for the diary entry
   * @param entityId Optional entity ID for updates
   * @returns The uploaded file path and registry entry
   */
  private async uploadDiaryQrFile(file: any, diaryEntryId: string, shareUrl: string, entityId?: string): Promise<{ filePath: string; registry: DiaryQrRegistry }> {
    try {
      this.logger.log(`Uploading diary QR file for entry ID ${diaryEntryId}`);

      // Create the diary QR directory if it doesn't exist
      const diaryQrDir = path.join(this.fileUtilService.getUploadDir(), 'diary-qr');
      if (!fs.existsSync(diaryQrDir)) {
        fs.mkdirSync(diaryQrDir, { recursive: true });
      }

      let filePath: string;
      let fileSize: number;
      let fileName: string;
      let mimeType: string;

      // Handle both Buffer and File objects
      if (Buffer.isBuffer(file)) {
        // Generate a unique filename
        fileName = `diary-qr-${diaryEntryId}-${Date.now()}.png`;
        filePath = path.join('diary-qr', fileName);
        const absolutePath = path.join(this.fileUtilService.getUploadDir(), filePath);

        // Write the buffer to disk
        fs.writeFileSync(absolutePath, file);
        fileSize = file.length;
        mimeType = 'image/png';
      } else {
        // Handle as a regular file upload
        this.fileUtilService.validateFile(file);

        // Generate a unique filename
        fileName = this.fileUtilService.generateUniqueFilename(file.originalname, `diary-qr-${diaryEntryId}`);
        filePath = this.fileUtilService.writeFileToDisk(file, 'diary-qr', fileName);
        fileSize = file.size;
        mimeType = file.mimetype;
      }

      this.logger.log(`File uploaded for diary QR with entry ID ${diaryEntryId}: ${filePath}`);

      // Register the file
      const registry = await this.registerDiaryQrFile(diaryEntryId, filePath, fileName, mimeType, fileSize, shareUrl);

      return {
        filePath,
        registry,
      };
    } catch (error) {
      this.logger.error(`Error uploading diary QR file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a profile picture in the registry
   * @param userId User ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @returns The created registry entry
   */
  private async registerProfilePicture(userId: string, filePath: string, fileName: string, mimeType: string, fileSize: number): Promise<ProfilePictureRegistry> {
    try {
      // Find the profile picture
      let profilePicture = await this.profilePictureRepository.findOne({
        where: { userId: userId },
      });

      // If no profile picture exists, create one
      if (!profilePicture) {
        profilePicture = this.profilePictureRepository.create({
          userId: userId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
        });
        profilePicture = await this.profilePictureRepository.save(profilePicture);
      } else {
        // Update the existing profile picture
        profilePicture.filePath = filePath;
        profilePicture.fileName = fileName;
        profilePicture.mimeType = mimeType;
        profilePicture.fileSize = fileSize;
        profilePicture = await this.profilePictureRepository.save(profilePicture);
      }

      // Check if a registry entry already exists
      let registry = await this.profilePictureRegistryRepository.findOne({
        where: { profilePictureId: profilePicture.id },
      });

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.profilePictureRegistryRepository.create({
          profilePictureId: profilePicture.id,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          userId: userId,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
      }

      // Save the registry entry
      return await this.profilePictureRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering profile picture: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a shop item file in the registry
   * @param shopItemId Shop item ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file (optional)
   * @returns The created registry entry
   */
  private async registerShopItemFile(shopItemId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, userId?: string): Promise<ShopItemRegistry> {
    try {
      this.logger.log(`Registering shop item file for item ID ${shopItemId}`);

      // Check if a registry entry already exists
      let registry = await this.shopItemRegistryRepository.findOne({
        where: { shopItemId: shopItemId },
      });

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.shopItemRegistryRepository.create({
          shopItemId: shopItemId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          userId: userId,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        registry.userId = userId;
      }

      // Save the registry entry
      return await this.shopItemRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering shop item file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a diary skin file in the registry
   * @param diarySkinId Diary skin ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file (optional)
   * @returns The created registry entry
   */
  private async registerDiarySkinFile(diarySkinId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, userId?: string): Promise<DiarySkinRegistry> {
    try {
      // Find the diary skin
      const diarySkin = await this.diarySkinRepository.findOne({
        where: { id: diarySkinId },
      });

      if (!diarySkin) {
        throw new Error(`Diary skin with ID ${diarySkinId} not found`);
      }

      // Update the diary skin's preview image path
      diarySkin.previewImagePath = filePath;
      await this.diarySkinRepository.save(diarySkin);

      // Check if a registry entry already exists
      let registry = await this.diarySkinRegistryRepository.findOne({
        where: { diarySkinId: diarySkinId },
      });

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.diarySkinRegistryRepository.create({
          diarySkinId: diarySkinId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          userId: userId,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        registry.userId = userId;
      }

      // Save the registry entry
      return await this.diarySkinRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering diary skin file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register a diary cover file in the registry
   * @param diaryId Diary ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file
   * @returns The created registry entry
   */
  private async registerDiaryCoverFile(diaryId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, userId: string): Promise<DiaryCoverRegistry> {
    try {
      // Find the diary
      const diary = await this.diaryRepository.findOne({
        where: { id: diaryId },
      });

      if (!diary) {
        throw new Error(`Diary with ID ${diaryId} not found`);
      }

      // Check if a registry entry already exists
      let registry = await this.diaryCoverRegistryRepository.findOne({
        where: { diaryId: diaryId },
      });

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.diaryCoverRegistryRepository.create({
          diaryId: diaryId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          userId: userId,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        registry.userId = userId;
      }

      return await this.diaryCoverRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering diary cover file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a profile picture from the registry
   * @param userId User ID
   * @returns The profile picture or registry entry if found
   */
  private async getProfilePicture(userId: string): Promise<any> {
    try {
      this.logger.log(`Getting profile picture for user ID: ${userId}`);

      // First try to find the registry entry directly
      const registry = await this.profilePictureRegistryRepository.findOne({
        where: { profilePictureId: userId },
        relations: ['profilePicture'],
      });

      if (registry) {
        this.logger.log(`Found registry entry for profile picture ID: ${userId}`);
        return registry;
      }

      // If not found by profile picture ID, try by user ID
      this.logger.log(`No registry entry found by profile picture ID, trying by user ID: ${userId}`);

      // Find the profile picture by user ID
      const profilePicture = await this.profilePictureRepository.findOne({
        where: { userId: userId },
      });

      if (!profilePicture) {
        this.logger.warn(`Profile picture not found for user ${userId}`);
        return null;
      }

      // Try to find a registry entry for this profile picture
      const profileRegistry = await this.profilePictureRegistryRepository.findOne({
        where: { profilePictureId: profilePicture.id },
      });

      // If the profile picture has a registry entry, return that
      if (profileRegistry) {
        this.logger.log(`Found registry entry for profile picture: ${profilePicture.id}`);
        return profileRegistry;
      }

      // If no registry entry exists, create a temporary registry-like object
      if (profilePicture.filePath) {
        this.logger.log(`Creating temporary registry object for profile picture: ${profilePicture.id}`);
        return {
          id: null,
          profilePictureId: profilePicture.id,
          profilePicture: profilePicture,
          filePath: profilePicture.filePath,
          fileName: path.basename(profilePicture.filePath),
          mimeType: this.getMimeTypeFromPath(profilePicture.filePath),
          fileSize: profilePicture.fileSize,
          userId: profilePicture.userId,
          createdAt: profilePicture.createdAt,
          updatedAt: profilePicture.updatedAt,
        };
      }

      this.logger.warn(`Profile picture found but has no file path: ${profilePicture.id}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting profile picture: ${error.message}`);
      return null;
    }
  }

  /**
   * Get a shop item file from the registry
   * @param shopItemId Shop item ID
   * @returns The shop item registry entry or null if not found
   */
  private async getShopItemFile(shopItemId: string): Promise<ShopItemRegistry | null> {
    try {
      this.logger.log(`Getting shop item file for item ID: ${shopItemId}`);

      // Try to find the registry entry for this shop item
      const registry = await this.shopItemRegistryRepository.findOne({
        where: { shopItemId: shopItemId },
        relations: ['shopItem'],
      });

      if (registry) {
        this.logger.log(`Found registry entry for shop item ID: ${shopItemId}`);
        return registry;
      }

      // If no registry entry exists, try to get the shop item directly
      // and create a temporary registry-like object with the file path
      this.logger.log(`No registry entry found, trying to get shop item directly: ${shopItemId}`);
      const shopItem = await this.shopItemRepository.findOne({
        where: { id: shopItemId },
      });

      if (shopItem && shopItem.filePath) {
        this.logger.log(`Found shop item with file path: ${shopItem.filePath}`);
        // Return a registry-like object with the file path
        return {
          id: null,
          shopItemId: shopItem.id,
          shopItem: shopItem,
          filePath: shopItem.filePath,
          fileName: path.basename(shopItem.filePath),
          mimeType: this.getMimeTypeFromPath(shopItem.filePath),
          fileSize: null,
          userId: null,
          createdAt: shopItem.createdAt,
          updatedAt: shopItem.updatedAt,
        } as ShopItemRegistry;
      }

      this.logger.warn(`No shop item or file path found for ID: ${shopItemId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting shop item file: ${error.message}`);
      return null;
    }
  }

  /**
   * Get a diary skin file from the registry
   * @param diarySkinId Diary skin ID
   * @returns The diary skin registry entry or null if not found
   */
  private async getDiarySkinFile(diarySkinId: string): Promise<DiarySkinRegistry | null> {
    try {
      this.logger.log(`Getting diary skin file for skin ID: ${diarySkinId}`);

      // Try to find the most recent registry entry for this diary skin
      const registry = await this.diarySkinRegistryRepository.findOne({
        where: { diarySkinId: diarySkinId },
        relations: ['diarySkin'],
        order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
      });

      if (registry) {
        this.logger.log(`Found registry entry for diary skin ID: ${diarySkinId}`);
        this.logger.log(`Diary skin registry details - filePath: ${registry.filePath}, fileName: ${registry.fileName}, updatedAt: ${registry.updatedAt}`);
        return registry;
      }

      // If no registry entry exists, try to get the diary skin directly
      // and create a temporary registry-like object with the file path
      this.logger.log(`No registry entry found, trying to get diary skin directly: ${diarySkinId}`);
      const diarySkin = await this.diarySkinRepository.findOne({
        where: { id: diarySkinId },
      });

      if (diarySkin && diarySkin.previewImagePath) {
        this.logger.log(`Found diary skin with preview image path: ${diarySkin.previewImagePath}`);
        // Return a registry-like object with the file path
        return {
          id: null,
          diarySkinId: diarySkin.id,
          diarySkin: diarySkin,
          filePath: diarySkin.previewImagePath,
          fileName: path.basename(diarySkin.previewImagePath),
          mimeType: this.getMimeTypeFromPath(diarySkin.previewImagePath),
          fileSize: null,
          userId: null,
          createdAt: diarySkin.createdAt,
          updatedAt: diarySkin.updatedAt,
        } as DiarySkinRegistry;
      }

      this.logger.warn(`No diary skin or preview image path found for ID: ${diarySkinId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting diary skin file: ${error.message}`);
      return null;
    }
  }

  /**
   * Register a student diary skin file in the registry
   * @param studentDiarySkinId Student diary skin ID
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file (optional)
   * @returns The created registry entry
   */
  private async registerStudentDiarySkinFile(studentDiarySkinId: string, filePath: string, fileName: string, mimeType: string, fileSize: number, userId?: string): Promise<StudentDiarySkinRegistry> {
    try {
      // Find the student diary skin
      const studentDiarySkin = await this.studentDiarySkinRepository.findOne({
        where: { id: studentDiarySkinId },
      });

      if (!studentDiarySkin) {
        throw new Error(`Student diary skin with ID ${studentDiarySkinId} not found`);
      }

      // Update the student diary skin's preview image path
      studentDiarySkin.previewImagePath = filePath;
      await this.studentDiarySkinRepository.save(studentDiarySkin);

      // Check if a registry entry already exists
      let registry = await this.studentDiarySkinRegistryRepository.findOne({
        where: { studentDiarySkinId: studentDiarySkinId },
      });

      // If no registry entry exists, create one
      if (!registry) {
        registry = this.studentDiarySkinRegistryRepository.create({
          studentDiarySkinId: studentDiarySkinId,
          studentId: studentDiarySkin.studentId,
          filePath: filePath,
          fileName: fileName,
          mimeType: mimeType,
          fileSize: fileSize,
          userId: userId,
        });
      } else {
        // Update the existing registry entry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        registry.userId = userId;
      }

      return await this.studentDiarySkinRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering student diary skin file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a student diary skin file from the registry
   * @param studentDiarySkinId Student diary skin ID
   * @returns The student diary skin registry entry or null if not found
   */
  private async getStudentDiarySkinFile(studentDiarySkinId: string): Promise<StudentDiarySkinRegistry | null> {
    try {
      this.logger.log(`Getting student diary skin file for skin ID: ${studentDiarySkinId}`);

      // Try to find the most recent registry entry for this student diary skin
      const registry = await this.studentDiarySkinRegistryRepository.findOne({
        where: { studentDiarySkinId: studentDiarySkinId },
        relations: ['studentDiarySkin'],
        order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
      });

      if (registry) {
        this.logger.log(`Found registry entry for student diary skin ID: ${studentDiarySkinId}`);
        this.logger.log(`Student diary skin registry details - filePath: ${registry.filePath}, fileName: ${registry.fileName}, updatedAt: ${registry.updatedAt}`);
        return registry;
      }

      // If no registry entry exists, try to get the student diary skin directly
      // and create a temporary registry-like object with the file path
      this.logger.log(`No registry entry found, trying to get student diary skin directly: ${studentDiarySkinId}`);
      const studentDiarySkin = await this.studentDiarySkinRepository.findOne({
        where: { id: studentDiarySkinId },
      });

      if (studentDiarySkin && studentDiarySkin.previewImagePath) {
        this.logger.log(`Found student diary skin with preview image path: ${studentDiarySkin.previewImagePath}`);
        // Return a registry-like object with the file path
        return {
          id: null,
          studentDiarySkinId: studentDiarySkin.id,
          studentDiarySkin: studentDiarySkin,
          studentId: studentDiarySkin.studentId,
          filePath: studentDiarySkin.previewImagePath,
          fileName: path.basename(studentDiarySkin.previewImagePath),
          mimeType: this.getMimeTypeFromPath(studentDiarySkin.previewImagePath),
          fileSize: null,
          userId: null,
          createdAt: studentDiarySkin.createdAt,
          updatedAt: studentDiarySkin.updatedAt,
        } as StudentDiarySkinRegistry;
      }

      this.logger.warn(`No student diary skin or preview image path found for ID: ${studentDiarySkinId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting student diary skin file: ${error.message}`);
      return null;
    }
  }

  /**
   * Get a diary cover file from the registry
   * @param diaryId Diary ID
   * @returns The diary cover registry entry or null if not found
   */
  private async getDiaryCoverFile(diaryId: string): Promise<DiaryCoverRegistry | null> {
    try {
      this.logger.log(`Getting diary cover file for diary ID: ${diaryId}`);

      // Try to find the most recent registry entry for this diary
      const registry = await this.diaryCoverRegistryRepository.findOne({
        where: { diaryId: diaryId },
        relations: ['diary'],
        order: { updatedAt: 'DESC' }, // Get the most recently updated registry entry
      });

      if (registry) {
        this.logger.log(`Found registry entry for diary cover ID: ${diaryId}`);
        return registry;
      }

      this.logger.warn(`No diary cover found for diary ID: ${diaryId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting diary cover file: ${error.message}`);
      return null;
    }
  }

  /**
   * Upload a profile picture
   * @param file The file to upload
   * @param userId The user ID
   * @param entityId Optional entity ID for updates (profile picture ID)
   * @returns The profile picture entity and registry entry
   */
  private async uploadProfilePicture(file: any, userId: string, entityId?: string): Promise<{ filePath: string; registry: ProfilePictureRegistry }> {
    try {
      // Validate the file
      this.fileUtilService.validateFile(file);

      // Generate a unique filename
      const filename = this.fileUtilService.generateUniqueFilename(file.originalname, `user_${userId}`);

      // Write the file to disk
      const relativePath = this.fileUtilService.writeFileToDisk(file, 'profile-pictures', filename);

      // Find existing profile picture
      let profilePicture = await this.profilePictureRepository.findOne({
        where: { userId: userId },
      });

      // If an existing profile picture is found, delete the old file
      if (profilePicture && profilePicture.filePath) {
        this.fileUtilService.deleteFile(profilePicture.filePath);
      }

      // Create or update the profile picture entity
      if (!profilePicture) {
        profilePicture = this.profilePictureRepository.create({
          userId: userId,
          filePath: relativePath,
          fileName: file.originalname,
          mimeType: file.mimetype,
          fileSize: file.size,
        });
      } else {
        profilePicture.filePath = relativePath;
        profilePicture.fileName = file.originalname;
        profilePicture.mimeType = file.mimetype;
        profilePicture.fileSize = file.size;
      }

      // Save the profile picture
      await this.profilePictureRepository.save(profilePicture);

      // Check if a registry entry already exists for this profile picture
      let registry = null;

      // First check if we have an existing registry entry by profile picture ID
      if (profilePicture.id) {
        registry = await this.profilePictureRegistryRepository.findOne({
          where: { profilePictureId: profilePicture.id },
        });
      }

      // If no registry found, register the profile picture in the registry
      if (registry) {
        // Update existing registry
        registry.filePath = relativePath;
        registry.fileName = file.originalname;
        registry.mimeType = file.mimetype;
        registry.fileSize = file.size;
        registry.userId = userId;

        // Save the updated registry
        registry = await this.profilePictureRegistryRepository.save(registry);
        this.logger.log(`Updated existing registry entry for profile picture ID ${profilePicture.id}`);
      } else {
        // Create new registry entry
        registry = await this.registerProfilePicture(userId, relativePath, file.originalname, file.mimetype, file.size);
        this.logger.log(`Created new registry entry for profile picture ID ${profilePicture.id}`);
      }

      this.logger.log(`Profile picture uploaded for user ${userId}: ${relativePath}`);
      return { filePath: relativePath, registry };
    } catch (error) {
      this.logger.error(`Failed to upload profile picture for user ${userId}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload a file for a shop item
   * @param file File to upload
   * @param itemNumber Item number for reference
   * @param categoryId Optional category ID for organizing files
   * @param userId Optional user ID who uploaded the file
   * @param entityId Optional entity ID for updates (shop item ID)
   * @returns Path to the uploaded file
   */
  private async uploadShopItemFile(file: any, itemNumber: string, categoryId?: string, userId?: string, entityId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      // Check if file is valid
      if (!file || !file.buffer) {
        this.logger.error(`Invalid file provided for shop item ${itemNumber}`);
        throw new Error('Invalid file provided. File buffer is missing.');
      }

      // Log file details for debugging
      this.logger.log(`Processing file upload for shop item ${itemNumber}:`);
      this.logger.log(`  Original name: ${file.originalname || 'unknown'}`);
      this.logger.log(`  MIME type: ${file.mimetype || 'unknown'}`);
      this.logger.log(`  Size: ${file.size || 'unknown'} bytes`);

      // Validate the file
      this.fileUtilService.validateFile(file);

      // Base directory for shop item files
      let shopItemsDir = 'shop-items';

      // If category ID is provided, add it to the directory path
      if (categoryId) {
        // Get the category name
        const category = await this.shopCategoryRepository.findOne({
          where: { id: categoryId },
        });

        if (category) {
          // Create a subdirectory using the category name
          const categoryName = category.name || 'general';
          shopItemsDir = `${shopItemsDir}/${categoryName.toLowerCase().replace(/\s+/g, '-')}`;
        }
      }

      // Generate a unique filename using the item number
      const fileExtension = file.originalname ? file.originalname.split('.').pop() : 'bin';
      const filename = `${itemNumber}-${Date.now()}.${fileExtension}`;

      // Write the file to disk
      const relativePath = this.fileUtilService.writeFileToDisk(file, shopItemsDir, filename);

      this.logger.log(`File uploaded for shop item ${itemNumber}: ${relativePath}`);

      // If entityId is provided, check for existing registry entry
      let registry = null;
      if (entityId) {
        registry = await this.shopItemRegistryRepository.findOne({
          where: { shopItemId: entityId },
        });

        if (registry) {
          this.logger.log(`Found existing registry entry for shop item ID ${entityId}`);

          // Delete the old file if it exists
          if (registry.filePath) {
            this.fileUtilService.deleteFile(registry.filePath);
          }

          // Update the existing registry entry
          registry.filePath = relativePath;
          registry.fileName = file.originalname || filename;
          registry.mimeType = file.mimetype || 'application/octet-stream';
          registry.fileSize = file.size || 0;
          registry.userId = userId;

          // Save the updated registry
          registry = await this.shopItemRegistryRepository.save(registry);
          this.logger.log(`Updated existing registry entry for shop item ID ${entityId}`);

          return {
            filePath: relativePath,
            registry: registry,
          };
        }
      }

      // If no existing registry or entityId not provided, return the file info
      // The calling service will handle creating a new registry entry
      return {
        filePath: relativePath,
        registry: {
          filePath: relativePath,
          fileName: file.originalname || filename,
          mimeType: file.mimetype || 'application/octet-stream',
          fileSize: file.size || 0,
          userId: userId,
        },
      };
    } catch (error) {
      this.logger.error(`Error uploading file for shop item ${itemNumber}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload a file for a diary skin
   * @param file File to upload
   * @param diarySkinName Diary skin name
   * @param isStudentSkin Whether this is a student skin
   * @param userId User ID who uploaded the file
   * @returns Path to the uploaded file and file metadata
   */
  /**
   * Get MIME type from file path based on extension
   * @param filePath Path to the file
   * @returns MIME type string
   */
  private getMimeTypeFromPath(filePath: string): string {
    if (!filePath) return 'application/octet-stream';

    const ext = path.extname(filePath).toLowerCase();
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.svg':
        return 'image/svg+xml';
      case '.pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }

  private async uploadDiarySkinFile(file: any, diarySkinName: string, isStudentSkin: boolean = false, userId?: string, entityId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      this.logger.log(`Starting uploadDiarySkinFile - diarySkinName: ${diarySkinName}, isStudentSkin: ${isStudentSkin}, userId: ${userId || 'none'}, entityId: ${entityId || 'none'}`);

      // Log file details
      if (file) {
        this.logger.log(`File details - originalname: ${file.originalname || 'unknown'}, mimetype: ${file.mimetype || 'unknown'}, size: ${file.size || 'unknown'}`);
      } else {
        this.logger.error('No file provided to uploadDiarySkinFile');
        throw new Error('No file provided');
      }

      // Validate the file with basic validation
      this.fileUtilService.validateFile(file, {
        maxSizeInMB: 2, // 2MB max size
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
      });

      this.logger.log('File validation passed');

      // Determine the directory based on whether this is a student skin
      const uploadDir = isStudentSkin ? 'student-diary-skins' : 'diary-skins';
      this.logger.log(`Using upload directory: ${uploadDir}`);

      // Generate a unique filename
      const prefix = isStudentSkin && userId ? `${userId}-${diarySkinName.toLowerCase().replace(/\s+/g, '-')}` : diarySkinName.toLowerCase().replace(/\s+/g, '-');
      const fileExtension = file.originalname.split('.').pop();
      const filename = `${prefix}-${Date.now()}.${fileExtension}`;
      this.logger.log(`Generated filename: ${filename}`);

      // Write the file to disk
      this.logger.log(`Writing file to disk in directory: ${uploadDir} with filename: ${filename}`);
      const relativePath = this.fileUtilService.writeFileToDisk(file, uploadDir, filename);

      this.logger.log(`File uploaded for diary skin ${diarySkinName}: ${relativePath}`);

      // If entityId is provided, check for existing registry entry
      let registry = null;
      if (entityId) {
        registry = await this.diarySkinRegistryRepository.findOne({
          where: { diarySkinId: entityId },
        });

        if (registry) {
          this.logger.log(`Found existing registry entry for diary skin ID ${entityId}`);

          // We'll skip deleting the old file to avoid potential issues
          // The file cleanup can be handled by a scheduled task later

          // Update the existing registry entry
          registry.filePath = relativePath;
          registry.fileName = file.originalname;
          registry.mimeType = file.mimetype;
          registry.fileSize = file.size;
          registry.userId = userId;

          // Save the updated registry
          registry = await this.diarySkinRegistryRepository.save(registry);
          this.logger.log(`Updated existing registry entry for diary skin ID ${entityId}`);

          return {
            filePath: relativePath,
            registry: registry,
          };
        }
      }

      // If no existing registry or entityId not provided, return the file info
      // The calling service will handle creating a new registry entry
      return {
        filePath: relativePath,
        registry: {
          filePath: relativePath,
          fileName: file.originalname,
          mimeType: file.mimetype,
          fileSize: file.size,
          userId: userId,
        },
      };
    } catch (error) {
      this.logger.error(`Error uploading file for diary skin ${diarySkinName}: ${error.message}`);
      this.logger.error(`File details - Name: ${file?.originalname || 'unknown'}, Size: ${file?.size || 'unknown'}, Type: ${file?.mimetype || 'unknown'}`);
      this.logger.error(`Upload parameters - isStudentSkin: ${isStudentSkin}, userId: ${userId || 'none'}, entityId: ${entityId || 'none'}`);

      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }

      // Rethrow with more context
      throw new Error(`Failed to upload diary skin image: ${error.message}. Check server logs for more details.`);
    }
  }

  /**
   * Upload a file for a student diary skin
   * @param file File to upload
   * @param studentDiarySkinId Student diary skin ID
   * @param userId User ID who uploaded the file
   * @param entityId Optional entity ID for updates (student diary skin ID)
   * @returns Path to the uploaded file and registry entry
   */
  private async uploadStudentDiarySkinFile(file: any, studentDiarySkinId: string, userId?: string, entityId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      this.logger.log(`Starting uploadStudentDiarySkinFile - studentDiarySkinId: ${studentDiarySkinId}, userId: ${userId || 'none'}, entityId: ${entityId || 'none'}`);

      // Log file details
      if (file) {
        this.logger.log(`File details - originalname: ${file.originalname || 'unknown'}, mimetype: ${file.mimetype || 'unknown'}, size: ${file.size || 'unknown'}`);
      } else {
        this.logger.error('No file provided to uploadStudentDiarySkinFile');
        throw new Error('No file provided');
      }

      // Validate the file with basic validation
      this.fileUtilService.validateFile(file, {
        maxSizeInMB: 2, // 2MB max size
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
      });

      this.logger.log('File validation passed');

      // Use student-diary-skins directory
      const uploadDir = 'student-diary-skins';
      this.logger.log(`Using upload directory: ${uploadDir}`);

      // Generate a unique filename
      const prefix = userId ? `${userId}-${studentDiarySkinId}` : studentDiarySkinId;
      const fileExtension = file.originalname.split('.').pop();
      const filename = `${prefix}-${Date.now()}.${fileExtension}`;
      this.logger.log(`Generated filename: ${filename}`);

      // Write the file to disk
      this.logger.log(`Writing file to disk in directory: ${uploadDir} with filename: ${filename}`);
      const relativePath = this.fileUtilService.writeFileToDisk(file, uploadDir, filename);

      this.logger.log(`File uploaded for student diary skin ${studentDiarySkinId}: ${relativePath}`);

      // If entityId is provided, check for existing registry entry
      let registry = null;
      if (entityId) {
        registry = await this.studentDiarySkinRegistryRepository.findOne({
          where: { studentDiarySkinId: entityId },
        });

        if (registry) {
          this.logger.log(`Found existing registry entry for student diary skin ID ${entityId}`);

          // We'll skip deleting the old file to avoid potential issues
          // The file cleanup can be handled by a scheduled task later

          // Update the existing registry entry
          registry.filePath = relativePath;
          registry.fileName = file.originalname;
          registry.mimeType = file.mimetype;
          registry.fileSize = file.size;
          registry.userId = userId;

          // Save the updated registry
          registry = await this.studentDiarySkinRegistryRepository.save(registry);
          this.logger.log(`Updated existing registry entry for student diary skin ID ${entityId}`);

          return {
            filePath: relativePath,
            registry: registry,
          };
        }
      }

      // If no existing registry or entityId not provided, create a new registry entry
      registry = await this.registerStudentDiarySkinFile(
        studentDiarySkinId,
        relativePath,
        file.originalname,
        file.mimetype,
        file.size,
        userId
      );

      this.logger.log(`Created new registry entry for student diary skin ID ${studentDiarySkinId}`);

      return {
        filePath: relativePath,
        registry: registry,
      };
    } catch (error) {
      this.logger.error(`Error uploading file for student diary skin ${studentDiarySkinId}: ${error.message}`);
      this.logger.error(`File details - Name: ${file?.originalname || 'unknown'}, Size: ${file?.size || 'unknown'}, Type: ${file?.mimetype || 'unknown'}`);
      this.logger.error(`Upload parameters - userId: ${userId || 'none'}, entityId: ${entityId || 'none'}`);

      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }

      // Rethrow with more context
      throw new Error(`Failed to upload student diary skin image: ${error.message}. Check server logs for more details.`);
    }
  }

  /**
   * Upload a file for a diary cover
   * @param file File to upload
   * @param diaryId Diary ID
   * @param userId User ID who uploaded the file
   * @param entityId Optional entity ID for updates (diary ID)
   * @returns Path to the uploaded file and registry entry
   */
  private async uploadDiaryCoverFile(file: any, diaryId: string, userId: string, entityId?: string): Promise<{ filePath: string; registry: any }> {
    try {
      this.logger.log(`Starting uploadDiaryCoverFile - diaryId: ${diaryId}, userId: ${userId}, entityId: ${entityId || 'none'}`);

      // Log file details
      if (file) {
        this.logger.log(`File details - originalname: ${file.originalname || 'unknown'}, mimetype: ${file.mimetype || 'unknown'}, size: ${file.size || 'unknown'}`);
      } else {
        this.logger.error('No file provided to uploadDiaryCoverFile');
        throw new Error('No file provided');
      }

      // Validate the file with basic validation
      this.fileUtilService.validateFile(file, {
        maxSizeInMB: 5, // 5MB max size for cover photos
        allowedMimeTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      });

      this.logger.log('File validation passed');

      // Use diary-covers directory
      const uploadDir = 'diary-covers';
      this.logger.log(`Using upload directory: ${uploadDir}`);

      // Generate a unique filename using the same method as other file types
      const filename = this.fileUtilService.generateUniqueFilename(file.originalname, `diary-${diaryId}`);
      this.logger.log(`Generated filename: ${filename}`);

      // Write the file to disk
      this.logger.log(`Writing file to disk in directory: ${uploadDir} with filename: ${filename}`);
      const relativePath = this.fileUtilService.writeFileToDisk(file, uploadDir, filename);

      this.logger.log(`File uploaded for diary cover ${diaryId}: ${relativePath}`);

      // Check for existing registry entry
      let registry = await this.diaryCoverRegistryRepository.findOne({
        where: { diaryId: diaryId },
      });

      if (registry) {
        this.logger.log(`Found existing registry entry for diary ID ${diaryId}`);

        // Delete the old file if it exists
        if (registry.filePath) {
          this.fileUtilService.deleteFile(registry.filePath);
        }

        // Update the existing registry entry
        registry.filePath = relativePath;
        registry.fileName = file.originalname;
        registry.mimeType = file.mimetype;
        registry.fileSize = file.size;
        registry.userId = userId;

        // Save the updated registry
        registry = await this.diaryCoverRegistryRepository.save(registry);
        this.logger.log(`Updated existing registry entry for diary ID ${diaryId}`);
      } else {
        // Create new registry entry
        registry = this.diaryCoverRegistryRepository.create({
          diaryId: diaryId,
          filePath: relativePath,
          fileName: file.originalname,
          mimeType: file.mimetype,
          fileSize: file.size,
          userId: userId,
        });

        registry = await this.diaryCoverRegistryRepository.save(registry);
        this.logger.log(`Created new registry entry for diary ID ${diaryId}`);
      }

      return {
        filePath: relativePath,
        registry: registry,
      };
    } catch (error) {
      this.logger.error(`Error uploading file for diary cover ${diaryId}: ${error.message}`);
      this.logger.error(`File details - Name: ${file?.originalname || 'unknown'}, Size: ${file?.size || 'unknown'}, Type: ${file?.mimetype || 'unknown'}`);
      this.logger.error(`Upload parameters - userId: ${userId || 'none'}, entityId: ${entityId || 'none'}`);

      if (error.stack) {
        this.logger.error(`Stack trace: ${error.stack}`);
      }

      // Rethrow with more context
      throw new Error(`Failed to upload diary cover image: ${error.message}. Check server logs for more details.`);
    }
  }

  /**
   * Delete a diary cover registry entry
   * @param registryId Registry entry ID
   */
  async deleteDiaryCoverRegistry(registryId: string): Promise<void> {
    try {
      await this.diaryCoverRegistryRepository.delete(registryId);
      this.logger.log(`Deleted diary cover registry entry: ${registryId}`);
    } catch (error) {
      this.logger.error(`Error deleting diary cover registry entry: ${error.message}`);
      throw error;
    }
  }

  // S3 Registration Methods

  /**
   * Register profile picture with S3 metadata
   */
  private async registerProfilePictureWithS3(userId: string, uploadResult: any, file: any): Promise<ProfilePictureRegistry> {
    const registry = this.profilePictureRegistryRepository.create({
      profilePictureId: userId,
      userId: userId,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key, // For backward compatibility
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.profilePictureRegistryRepository.save(registry);
  }

  /**
   * Register shop item with S3 metadata
   */
  private async registerShopItemWithS3(shopItemId: string, uploadResult: any, file: any, options: any): Promise<ShopItemRegistry> {
    // For shop items, shopItemId should be the UUID, not the item number
    // If shopItemId looks like an item number (e.g., "ST-999"), we need to find the actual shop item UUID
    let actualShopItemId = shopItemId;

    // Check if the provided ID looks like an item number instead of a UUID
    if (shopItemId && !shopItemId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      // This looks like an item number, try to find the shop item by item number
      const shopItem = await this.shopItemRepository.findOne({
        where: { itemNumber: shopItemId }
      });

      if (shopItem) {
        actualShopItemId = shopItem.id;
        this.logger.log(`Found shop item UUID ${actualShopItemId} for item number ${shopItemId}`);
      } else {
        this.logger.error(`Shop item not found for item number: ${shopItemId}`);
        throw new Error(`Shop item not found for item number: ${shopItemId}`);
      }
    }

    const registry = this.shopItemRegistryRepository.create({
      shopItemId: actualShopItemId,
      userId: options?.userId,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key,
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.shopItemRegistryRepository.save(registry);
  }

  /**
   * Register diary skin with S3 metadata
   */
  private async registerDiarySkinWithS3(entityId: string, uploadResult: any, file: any, options: any): Promise<DiarySkinRegistry> {
    // Validate that entityId is a valid UUID format for diary skins
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!entityId || !uuidRegex.test(entityId)) {
      const errorMsg = `Invalid diary skin ID: "${entityId}". Expected UUID format, but received: ${typeof entityId}`;
      this.logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    const registry = this.diarySkinRegistryRepository.create({
      diarySkinId: entityId, // Use the actual diary skin UUID, not the name
      userId: options?.userId,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key,
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.diarySkinRegistryRepository.save(registry);
  }

  /**
   * Register student diary skin with S3 metadata
   */
  private async registerStudentDiarySkinWithS3(entityId: string, uploadResult: any, file: any, options: any): Promise<StudentDiarySkinRegistry> {
    // Validate that entityId is a valid UUID format for student diary skins
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!entityId || !uuidRegex.test(entityId)) {
      const errorMsg = `Invalid student diary skin ID: "${entityId}". Expected UUID format, but received: ${typeof entityId}`;
      this.logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    // Get the student diary skin to get the student ID
    const studentDiarySkin = await this.studentDiarySkinRepository.findOne({
      where: { id: entityId },
    });

    if (!studentDiarySkin) {
      throw new Error(`Student diary skin with ID ${entityId} not found`);
    }

    const registry = this.studentDiarySkinRegistryRepository.create({
      studentDiarySkinId: entityId, // Use the actual student diary skin UUID
      studentId: studentDiarySkin.studentId,
      userId: options?.userId,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key,
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.studentDiarySkinRegistryRepository.save(registry);
  }

  /**
   * Register diary cover with S3 metadata
   */
  private async registerDiaryCoverWithS3(diaryId: string, uploadResult: any, file: any, options: any): Promise<DiaryCoverRegistry> {
    // Check for existing registry entry first
    let registry = await this.diaryCoverRegistryRepository.findOne({
      where: { diaryId: diaryId }
    });

    if (registry) {
      // Update existing registry
      registry.filePath = uploadResult.key;
      registry.fileName = file.originalname;
      registry.mimeType = file.mimetype;
      registry.fileSize = file.size;
      registry.userId = options?.userId;
      registry.storageProvider = StorageProvider.S3;
      registry.storageKey = uploadResult.key;
      registry.s3Bucket = uploadResult.bucket;
      registry.s3Region = uploadResult.region;
      registry.s3Etag = uploadResult.etag;
      registry.s3VersionId = uploadResult.versionId;
      registry.s3StorageClass = uploadResult.storageClass;
      registry.s3ServerSideEncryption = uploadResult.serverSideEncryption;
      registry.cdnUrl = uploadResult.url;
    } else {
      // Create new registry
      registry = this.diaryCoverRegistryRepository.create({
        diaryId: diaryId,
        userId: options?.userId,
        fileName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        filePath: uploadResult.key,
        storageProvider: StorageProvider.S3,
        storageKey: uploadResult.key,
        s3Bucket: uploadResult.bucket,
        s3Region: uploadResult.region,
        s3Etag: uploadResult.etag,
        s3VersionId: uploadResult.versionId,
        s3StorageClass: uploadResult.storageClass,
        s3ServerSideEncryption: uploadResult.serverSideEncryption,
        cdnUrl: uploadResult.url,
        storageMetadata: uploadResult.metadata
      });
    }

    return await this.diaryCoverRegistryRepository.save(registry);
  }

  /**
   * Register diary QR with S3 metadata
   */
  private async registerDiaryQrWithS3(diaryEntryId: string, uploadResult: any, file: any, options: any): Promise<DiaryQrRegistry> {
    const registry = this.diaryQrRegistryRepository.create({
      diaryEntryId: diaryEntryId,
      shareUrl: options?.shareUrl,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key,
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.diaryQrRegistryRepository.save(registry);
  }

  /**
   * Register story maker with S3 metadata
   */
  private async registerStoryMakerWithS3(storyMakerId: string, uploadResult: any, file: any): Promise<StoryMakerRegistry> {
    // Validate that storyMakerId is a valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!storyMakerId || !uuidRegex.test(storyMakerId)) {
      const errorMsg = `Invalid story maker ID: "${storyMakerId}". Expected UUID format, but received: ${typeof storyMakerId}`;
      this.logger.error(errorMsg);
      throw new Error(errorMsg);
    }

    const registry = this.storyMakerRegistryRepository.create({
      storyMakerId: storyMakerId,
      fileName: file.originalname,
      mimeType: file.mimetype,
      fileSize: file.size,
      filePath: uploadResult.key,
      storageProvider: StorageProvider.S3,
      storageKey: uploadResult.key,
      s3Bucket: uploadResult.bucket,
      s3Region: uploadResult.region,
      s3Etag: uploadResult.etag,
      s3VersionId: uploadResult.versionId,
      s3StorageClass: uploadResult.storageClass,
      s3ServerSideEncryption: uploadResult.serverSideEncryption,
      cdnUrl: uploadResult.url,
      storageMetadata: uploadResult.metadata
    });

    return await this.storyMakerRegistryRepository.save(registry);
  }

  /**
   * Get message attachment file by registry ID
   * @param registryId Registry ID
   * @returns Registry entry or null if not found
   */
  private async getMessageAttachmentFile(registryId: string): Promise<MessageRegistry> {
    try {
      const registry = await this.messageRegistryRepository.findOne({
        where: { id: registryId }
      });

      if (registry) {
        return registry;
      }

      this.logger.warn(`No message attachment registry entry found for ID: ${registryId}`);
      return null;
    } catch (error) {
      this.logger.error(`Error getting message attachment file: ${error.message}`);
      return null;
    }
  }

  /**
   * Register message attachment
   * @param registryId Registry ID (can be existing or new)
   * @param filePath Path to the file
   * @param fileName Original file name
   * @param mimeType MIME type of the file
   * @param fileSize Size of the file in bytes
   * @param userId User ID who uploaded the file
   * @returns The created/updated registry entry
   */
  private async registerMessageAttachment(
    registryId: string,
    filePath: string,
    fileName: string,
    mimeType: string,
    fileSize: number,
    userId?: string
  ): Promise<MessageRegistry> {
    try {
      let registry = await this.messageRegistryRepository.findOne({
        where: { id: registryId }
      });

      if (!registry) {
        registry = this.messageRegistryRepository.create({
          userId,
          filePath,
          fileName,
          mimeType,
          fileSize,
          isTemporary: true
        });
      } else {
        // Update existing registry
        registry.filePath = filePath;
        registry.fileName = fileName;
        registry.mimeType = mimeType;
        registry.fileSize = fileSize;
        if (userId) registry.userId = userId;
      }

      return await this.messageRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering message attachment: ${error.message}`);
      throw error;
    }
  }

  /**
   * Upload message attachment file
   * @param file The file to upload
   * @param userId User ID who is uploading
   * @param options Additional options
   * @returns The uploaded file path and registry entry
   */
  private async uploadMessageAttachment(
    file: any,
    userId: string,
    options?: any
  ): Promise<{ filePath: string; registry: MessageRegistry }> {
    try {
      // Validate the file
      this.fileUtilService.validateFile(file, {
        maxSizeInMB: 10, // 10MB max size for message attachments
        allowedMimeTypes: [
          'image/jpeg', 'image/png', 'image/gif', 'image/webp',
          'application/pdf', 'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain', 'application/zip'
        ]
      });

      // Generate file path
      const timestamp = Date.now();
      const fileExt = path.extname(file.originalname);
      const fileName = `${path.basename(file.originalname, fileExt)}-${timestamp}${fileExt}`;
      const relativePath = path.join('chat', fileName);
      const filePath = path.join(this.fileUtilService.getUploadDir(), relativePath);

      // Ensure directory exists
      const dir = path.dirname(filePath);
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }

      // Write file
      fs.writeFileSync(filePath, file.buffer);

      // Create registry entry
      const registry = this.messageRegistryRepository.create({
        userId,
        filePath: relativePath,
        fileName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
        isTemporary: options?.isTemporary !== false
      });

      const savedRegistry = await this.messageRegistryRepository.save(registry);

      return {
        filePath: relativePath,
        registry: savedRegistry
      };
    } catch (error) {
      this.logger.error(`Error uploading message attachment: ${error.message}`);
      throw error;
    }
  }

  /**
   * Register message attachment with S3 metadata
   */
  private async registerMessageAttachmentWithS3(
    registryId: string,
    uploadResult: any,
    file: any,
    options?: any
  ): Promise<MessageRegistry> {
    try {
      let registry = await this.messageRegistryRepository.findOne({
        where: { id: registryId }
      });

      if (!registry) {
        registry = this.messageRegistryRepository.create({
          userId: options?.userId,
          messageId: options?.messageId,
          filePath: uploadResult.key,
          fileName: file.originalname,
          mimeType: file.mimetype,
          fileSize: file.size,
          isTemporary: options?.isTemporary !== false,
          storageProvider: StorageProvider.S3,
          storageKey: uploadResult.key,
          s3Bucket: uploadResult.bucket,
          s3Region: uploadResult.region,
          s3Etag: uploadResult.etag,
          s3VersionId: uploadResult.versionId,
          s3StorageClass: uploadResult.storageClass,
          s3ServerSideEncryption: uploadResult.serverSideEncryption,
          cdnUrl: uploadResult.url,
          storageMetadata: uploadResult.metadata
        });
      } else {
        // Update existing registry
        registry.filePath = uploadResult.key;
        registry.fileName = file.originalname;
        registry.mimeType = file.mimetype;
        registry.fileSize = file.size;
        registry.storageProvider = StorageProvider.S3;
        registry.storageKey = uploadResult.key;
        registry.s3Bucket = uploadResult.bucket;
        registry.s3Region = uploadResult.region;
        registry.s3Etag = uploadResult.etag;
        registry.s3VersionId = uploadResult.versionId;
        registry.s3StorageClass = uploadResult.storageClass;
        registry.s3ServerSideEncryption = uploadResult.serverSideEncryption;
        registry.cdnUrl = uploadResult.url;
        registry.storageMetadata = uploadResult.metadata;
      }

      return await this.messageRegistryRepository.save(registry);
    } catch (error) {
      this.logger.error(`Error registering message attachment with S3: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get message attachment URL (public method for chat service)
   * @param attachmentId Message attachment ID (from MessageRegistry)
   * @returns File URL
   */
  async getMessageAttachmentUrl(attachmentId: string): Promise<string> {
    try {
      return await this.getFileUrl(FileEntityType.MESSAGE_ATTACHMENT, attachmentId);
    } catch (error) {
      this.logger.error(`Error getting message attachment URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get file buffer for serving files
   * @param entityType Type of entity
   * @param entityId Entity ID
   * @returns File buffer and metadata
   */
  async getFileBuffer(entityType: FileEntityType, entityId: string): Promise<{ buffer: Buffer; fileName: string; mimeType: string }> {
    try {
      const registry = await this.getFileByRegistryId(entityType, entityId);

      if (!registry) {
        throw new Error(`File not found for ${entityType} with ID: ${entityId}`);
      }

      // Handle S3 files
      if (registry.storageProvider === StorageProvider.S3 && registry.storageKey) {
        // For S3 files, we'll need to implement file buffer retrieval
        // For now, throw an error indicating S3 direct buffer access is not implemented
        throw new Error('Direct S3 file buffer access not implemented. Use presigned URLs instead.');
      }

      // Handle local files
      const filePath = path.join(this.fileUtilService.getUploadDir(), registry.filePath);
      if (!fs.existsSync(filePath)) {
        throw new Error('File not found on disk');
      }

      const buffer = fs.readFileSync(filePath);
      return {
        buffer,
        fileName: registry.fileName,
        mimeType: registry.mimeType
      };
    } catch (error) {
      this.logger.error(`Error getting file buffer for ${entityType}:${entityId}: ${error.message}`);
      throw error;
    }
  }
}
