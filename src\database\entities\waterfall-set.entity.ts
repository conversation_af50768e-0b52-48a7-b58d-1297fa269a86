import { <PERSON><PERSON><PERSON>, Column, OneToMany } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { WaterfallQuestion } from './waterfall-question.entity';

@Entity()
export class WaterfallSet extends AuditableBaseEntity {
  @Column()
  title: string;

  @Column({ name: 'total_score' })
  totalScore: number;

  @Column({ name: 'total_questions' })
  totalQuestions: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @OneToMany(() => WaterfallQuestion, (question) => question.set, { cascade: true })
  questions: WaterfallQuestion[];
}
