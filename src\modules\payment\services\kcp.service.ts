import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { KcpConfigService } from './kcp-config.service';
import {
  KcpTradeRegRequest,
  KcpTradeRegResponse,
  KcpPaymentRequest,
  KcpPaymentResponse,
  PaymentInitiationRequest,
  PaymentInitiationResponse,
  KcpPaymentFormData
} from '../interfaces/kcp.interface';

@Injectable()
export class KcpService {
  private readonly logger = new Logger(KcpService.name);

  constructor(
    private readonly kcpConfig: KcpConfigService,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {}



  /**
   * Register trade with KCP
   */
  async registerTrade(request: PaymentInitiationRequest): Promise<KcpTradeRegResponse> {
    try {
      this.logger.log(`Registering trade for order: ${request.orderId}`);

      // Validate amount
      const amount = Math.floor(Math.abs(request.amount));
      if (amount <= 0) {
        throw new BadRequestException('Payment amount must be greater than 0');
      }
      if (amount > 99999999) {
        throw new BadRequestException('Payment amount is too large');
      }

      this.logger.log(`Validated amount: ${amount} (original: ${request.amount})`);

      const tradeRegRequest: KcpTradeRegRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        ordr_idxx: request.orderId,
        good_name: request.productName,
        good_mny: amount,
        buyr_name: request.buyerName,
        buyr_mail: request.buyerEmail,
        buyr_tel1: request.buyerPhone,
        currency: request.currency || 'KRW',
        shop_user_id: request.userId,
        pay_method: this.getPayMethodCode(request.paymentMethod),
        Ret_URL: request.returnUrl,
        user_agent: 'HEC-Backend/1.0',
        remote_addr: '127.0.0.1',
        escw_used: 'N'
      };

      // Make actual HTTP request to KCP trade registration API
      const kcpApiUrl = this.kcpConfig.getTradeRegUrl();

      const response = await this.retryKcpRequest(async () => {
        return await firstValueFrom(
          this.httpService.post(kcpApiUrl, tradeRegRequest, {
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'HEC-Backend/1.0'
            },
            timeout: this.kcpConfig.getTimeout()
          })
        );
      });

      // Parse KCP response
      const kcpResponse = this.parseKcpResponse(response.data);
      const { code: responseCode, message: responseMessage } = this.extractKcpResponseFields(kcpResponse);

      if (responseCode !== '0000') {
        throw new BadRequestException(`KCP Trade Registration failed: ${responseMessage} (Code: ${responseCode})`);
      }

      // Generate additional required fields for payment processing
      const tradeRegResponse: KcpTradeRegResponse = {
        res_cd: responseCode,
        res_msg: responseMessage,
        tno: kcpResponse.tno,
        amount: kcpResponse.amount || amount.toString(),
        pnt_issue: kcpResponse.pnt_issue || '0',
        trace_no: kcpResponse.trace_no,
        PayUrl: kcpResponse.PayUrl || this.kcpConfig.getPaymentUrl(),
        ordr_chk: kcpResponse.ordr_chk || this.kcpConfig.generateOrderCheck(request.orderId, amount),
        kcp_sign_data: kcpResponse.kcp_sign_data
      };

      this.logger.log(`Trade registered successfully: ${tradeRegResponse.tno}`);
      return tradeRegResponse;

    } catch (error) {
      this.logger.error(`Trade registration failed: ${error.message}`, error.stack);

      // Handle specific HTTP errors
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;
        this.logger.error(`KCP API HTTP Error - Status: ${status}, Data:`, data);

        if (status === 401 || status === 403) {
          throw new BadRequestException('KCP authentication failed. Please check your credentials.');
        } else if (status >= 500) {
          throw new InternalServerErrorException('KCP service is temporarily unavailable. Please try again later.');
        }
      }

      if (error instanceof BadRequestException || error instanceof InternalServerErrorException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to register trade with KCP');
    }
  }

  /**
   * Process payment with KCP
   * Note: This method is typically not called directly in KCP integration.
   * KCP payments are usually processed through frontend form submission to KCP's payment page.
   * This method is kept for completeness and special use cases.
   */
  async processPayment(
    tradeRegResponse: KcpTradeRegResponse,
    request: PaymentInitiationRequest
  ): Promise<KcpPaymentResponse> {
    try {
      this.logger.log(`Processing payment for transaction: ${tradeRegResponse.tno}`);

      // Validate amount
      const amount = Math.floor(Math.abs(request.amount));
      if (amount <= 0) {
        throw new BadRequestException('Payment amount must be greater than 0');
      }

      const paymentRequest: KcpPaymentRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        tran_cd: '00100000',
        ordr_idxx: request.orderId,
        good_name: request.productName,
        good_mny: amount,
        buyr_name: request.buyerName,
        buyr_mail: request.buyerEmail,
        buyr_tel1: request.buyerPhone,
        pay_method: this.getPayMethodCode(request.paymentMethod),
        Ret_URL: request.returnUrl,
        currency: request.currency || 'KRW',
        shop_user_id: request.userId,
        user_agent: 'HEC-Backend/1.0',
        remote_addr: '127.0.0.1',
        ordr_chk: tradeRegResponse.ordr_chk,
        kcp_sign_data: tradeRegResponse.kcp_sign_data,
        escw_used: 'N'
      };

      // Make actual HTTP request to KCP payment API
      const kcpPaymentUrl = this.kcpConfig.getPaymentUrl();
      this.logger.log(`Making KCP payment request to: ${kcpPaymentUrl}`);

      // Convert request object to URL-encoded form data
      const formData = new URLSearchParams();
      Object.keys(paymentRequest).forEach(key => {
        const value = paymentRequest[key];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      const response = await this.retryKcpRequest(async () => {
        return await firstValueFrom(
          this.httpService.post(kcpPaymentUrl, formData.toString(), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'HEC-Backend/1.0'
            },
            timeout: this.kcpConfig.getTimeout()
          })
        );
      });

      this.logger.log(`KCP Payment API Response Status: ${response.status}`);
      this.logger.log(`KCP Payment API Response Data:`, response.data);

      // Parse KCP response
      const kcpResponse = this.parseKcpResponse(response.data);

      // Handle different KCP response formats
      const responseCode = kcpResponse.res_cd || kcpResponse.Code;
      const responseMessage = kcpResponse.res_msg || kcpResponse.Message;

      if (responseCode !== '0000') {
        this.logger.error(`KCP Payment failed - Code: ${responseCode}, Message: ${responseMessage}`);
        throw new BadRequestException(`KCP Payment failed: ${responseMessage} (Code: ${responseCode})`);
      }

      const paymentResponse: KcpPaymentResponse = {
        res_cd: responseCode,
        res_msg: responseMessage,
        tno: kcpResponse.tno || tradeRegResponse.tno,
        amount: kcpResponse.amount || amount.toString(),
        pnt_issue: kcpResponse.pnt_issue || '0',
        trace_no: kcpResponse.trace_no,
        app_time: kcpResponse.app_time,
        app_no: kcpResponse.app_no,
        card_cd: kcpResponse.card_cd,
        card_name: kcpResponse.card_name,
        card_no: kcpResponse.card_no,
        bank_name: kcpResponse.bank_name,
        bank_code: kcpResponse.bank_code
      };

      this.logger.log(`Payment processed successfully: ${paymentResponse.tno}`);
      return paymentResponse;

    } catch (error) {
      this.logger.error(`Payment processing failed: ${error.message}`, error.stack);

      // Handle specific HTTP errors
      if (error.response) {
        const status = error.response.status;
        const data = error.response.data;
        this.logger.error(`KCP Payment API HTTP Error - Status: ${status}, Data:`, data);

        if (status === 401 || status === 403) {
          throw new BadRequestException('KCP authentication failed. Please check your credentials.');
        } else if (status >= 500) {
          throw new InternalServerErrorException('KCP service is temporarily unavailable. Please try again later.');
        }
      }

      if (error instanceof BadRequestException || error instanceof InternalServerErrorException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to process payment with KCP');
    }
  }

  /**
   * Initiate payment process
   */
  async initiatePayment(request: PaymentInitiationRequest): Promise<PaymentInitiationResponse> {
    try {
      this.logger.log(`Initiating payment for order: ${request.orderId}`);

      // Step 1: Register trade
      const tradeRegResponse = await this.registerTrade(request);

      // Step 2: Generate payment form data and URL
      const { paymentUrl, formData } = await this.generatePaymentData(tradeRegResponse, request);

      // Calculate expiration time (30 minutes from now)
      const expiresAt = new Date(Date.now() + 30 * 60 * 1000);

      return {
        success: true,
        transactionId: tradeRegResponse.tno,
        paymentUrl: paymentUrl,
        redirectUrl: paymentUrl,
        message: 'Payment initiated successfully',
        expiresAt: expiresAt,
        kcpFormData: formData
      };

    } catch (error) {
      this.logger.error(`Payment initiation failed: ${error.message}`, error.stack);
      return {
        success: false,
        transactionId: '',
        message: error.message || 'Payment initiation failed',
        errorCode: error.code || 'PAYMENT_INIT_ERROR'
      };
    }
  }

  /**
   * Verify payment result with KCP API
   */
  async verifyPayment(kcpResponse: any): Promise<boolean> {
    try {
      this.logger.log(`Verifying payment with KCP API: ${kcpResponse.tno}`);

      // Step 1: Basic response validation
      if (!kcpResponse.tno || !kcpResponse.ordr_idxx) {
        this.logger.error('Missing required fields in KCP response');
        return false;
      }

      // Step 2: Check if payment was successful according to KCP response
      const responseCode = kcpResponse.res_cd || kcpResponse.Code;
      const responseMessage = kcpResponse.res_msg || kcpResponse.Message;

      if (responseCode !== '0000') {
        this.logger.warn(`Payment failed according to KCP response - Code: ${responseCode}, Message: ${responseMessage}`);
        return false;
      }

      // Step 3: Make API call to KCP to verify transaction status
      const verificationResult = await this.verifyTransactionWithKcp(kcpResponse.tno, kcpResponse.ordr_idxx);

      if (!verificationResult) {
        this.logger.error(`KCP API verification failed for transaction: ${kcpResponse.tno}`);
        return false;
      }

      // Step 4: Validate signature if present
      if (kcpResponse.kcp_sign_data) {
        const isSignatureValid = this.validateKcpSignature(kcpResponse);
        if (!isSignatureValid) {
          this.logger.error(`Invalid KCP signature for transaction: ${kcpResponse.tno}`);
          return false;
        }
      }

      this.logger.log(`Payment verified successfully: ${kcpResponse.tno}`);
      return true;

    } catch (error) {
      this.logger.error(`Payment verification error: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Verify transaction status with KCP API
   */
  private async verifyTransactionWithKcp(tno: string, orderId: string): Promise<boolean> {
    try {
      this.logger.log(`Verifying transaction with KCP API: ${tno}`);

      // KCP transaction verification request
      const verificationRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        tran_cd: '00300000', // Transaction inquiry code
        tno: tno,
        ordr_idxx: orderId
      };

      // Convert to form data
      const formData = new URLSearchParams();
      Object.keys(verificationRequest).forEach(key => {
        const value = verificationRequest[key];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Make API call to KCP verification endpoint
      const kcpApiUrl = `${this.kcpConfig.getApiUrl()}/std/inquiry/inquiry.jsp`;

      const response = await this.retryKcpRequest(async () => {
        return await firstValueFrom(
          this.httpService.post(kcpApiUrl, formData.toString(), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'HEC-Backend/1.0'
            },
            timeout: this.kcpConfig.getTimeout()
          })
        );
      });

      const kcpResponse = this.parseKcpResponse(response.data);

      // Handle different KCP response formats
      const responseCode = kcpResponse.res_cd || kcpResponse.Code;
      const responseMessage = kcpResponse.res_msg || kcpResponse.Message;

      // Check if verification was successful
      if (responseCode === '0000') {
        this.logger.log(`Transaction verified with KCP: ${tno}`);
        return true;
      } else {
        this.logger.error(`KCP verification failed - Code: ${responseCode}, Message: ${responseMessage}`);
        return false;
      }

    } catch (error) {
      this.logger.error(`KCP transaction verification failed: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Validate KCP signature using real KCP algorithm
   */
  private validateKcpSignature(kcpResponse: any): boolean {
    try {
      // If no signature provided, skip validation
      if (!kcpResponse.kcp_sign_data) {
        this.logger.log('No KCP signature provided, skipping validation');
        return true;
      }

      this.logger.log(`Validating KCP signature for transaction: ${kcpResponse.tno}`);

      // Real KCP signature validation algorithm
      // KCP uses a specific algorithm to generate signatures based on transaction data
      const crypto = require('crypto');

      // Build signature data string according to KCP specification
      const signatureData = [
        kcpResponse.site_cd || this.kcpConfig.getSiteCd(),
        kcpResponse.ordr_idxx || '',
        kcpResponse.amount || '0',
        kcpResponse.tno || '',
        this.kcpConfig.getSiteKey()
      ].join('');

      // Generate expected signature using SHA256 (KCP standard)
      const expectedSignature = crypto
        .createHash('sha256')
        .update(signatureData, 'utf8')
        .digest('hex')
        .toUpperCase();

      const receivedSignature = kcpResponse.kcp_sign_data.toUpperCase();

      this.logger.debug(`Signature validation - Expected: ${expectedSignature}, Received: ${receivedSignature}`);

      // Compare signatures
      const isValid = expectedSignature === receivedSignature;

      if (isValid) {
        this.logger.log(`KCP signature validation successful for transaction: ${kcpResponse.tno}`);
      } else {
        this.logger.error(`KCP signature validation failed for transaction: ${kcpResponse.tno}`);
        this.logger.error(`Expected: ${expectedSignature}, Received: ${receivedSignature}`);
      }

      return isValid;

    } catch (error) {
      this.logger.error(`Error validating KCP signature: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Cancel payment
   */
  async cancelPayment(transactionId: string, reason: string): Promise<boolean> {
    try {
      this.logger.log(`Cancelling payment: ${transactionId}, reason: ${reason}`);

      // KCP cancellation request
      const cancelRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        tran_cd: '00200000', // Cancellation transaction code
        tno: transactionId,
        mod_type: 'STSC', // Cancel type
        mod_desc: reason || 'Payment cancellation requested'
      };

      // Convert to form data
      const formData = new URLSearchParams();
      Object.keys(cancelRequest).forEach(key => {
        const value = cancelRequest[key];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Make API call to KCP cancellation endpoint
      const kcpApiUrl = `${this.kcpConfig.getApiUrl()}/std/mod/mod.jsp`;

      const response = await this.retryKcpRequest(async () => {
        return await firstValueFrom(
          this.httpService.post(kcpApiUrl, formData.toString(), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'HEC-Backend/1.0'
            },
            timeout: this.kcpConfig.getTimeout()
          })
        );
      });

      const kcpResponse = this.parseKcpResponse(response.data);

      // Handle different KCP response formats
      const responseCode = kcpResponse.res_cd || kcpResponse.Code;
      const responseMessage = kcpResponse.res_msg || kcpResponse.Message;

      if (responseCode === '0000') {
        this.logger.log(`Payment cancelled successfully: ${transactionId}`);
        return true;
      } else {
        this.logger.error(`Payment cancellation failed - Code: ${responseCode}, Message: ${responseMessage}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Payment cancellation failed: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Process refund
   */
  async processRefund(transactionId: string, amount: number, reason: string): Promise<any> {
    try {
      this.logger.log(`Processing refund for transaction: ${transactionId}, amount: ${amount}, reason: ${reason}`);

      // KCP refund request
      const refundRequest = {
        site_cd: this.kcpConfig.getSiteCd(),
        kcp_cert_info: this.kcpConfig.getKcpCertInfo(),
        tran_cd: '00200000', // Modification transaction code
        tno: transactionId,
        mod_type: 'STPC', // Partial cancellation type
        mod_mny: amount.toString(),
        mod_desc: reason || 'Refund requested'
      };

      // Convert to form data
      const formData = new URLSearchParams();
      Object.keys(refundRequest).forEach(key => {
        const value = refundRequest[key];
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Make API call to KCP modification endpoint
      const kcpApiUrl = `${this.kcpConfig.getApiUrl()}/std/mod/mod.jsp`;

      const response = await this.retryKcpRequest(async () => {
        return await firstValueFrom(
          this.httpService.post(kcpApiUrl, formData.toString(), {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': 'HEC-Backend/1.0'
            },
            timeout: this.kcpConfig.getTimeout()
          })
        );
      });

      const kcpResponse = this.parseKcpResponse(response.data);

      // Handle different KCP response formats
      const responseCode = kcpResponse.res_cd || kcpResponse.Code;
      const responseMessage = kcpResponse.res_msg || kcpResponse.Message;

      if (responseCode === '0000') {
        this.logger.log(`Refund processed successfully: ${transactionId}`);
        return {
          success: true,
          refundId: kcpResponse.tno || `REF-${Date.now()}`,
          amount: amount,
          transactionId: transactionId,
          message: 'Refund processed successfully',
          kcpResponse: kcpResponse
        };
      } else {
        this.logger.error(`Refund processing failed - Code: ${responseCode}, Message: ${responseMessage}`);
        throw new BadRequestException(`Refund failed: ${responseMessage} (Code: ${responseCode})`);
      }
    } catch (error) {
      this.logger.error(`Refund processing failed: ${error.message}`, error.stack);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to process refund');
    }
  }

  /**
   * Get payment method code for KCP
   */
  private getPayMethodCode(paymentMethod: string): string {
    const payMethodMap = {
      'card': '************',    // Credit card
      'bank': '************',    // Bank transfer
      'mobile': '************',  // Mobile payment
      'vacct': '************'    // Virtual account
    };

    return payMethodMap[paymentMethod] || '************';
  }

  /**
   * Generate payment form data for KCP frontend integration
   * Based on KCP's official sample, we return form data instead of direct URL
   */
  private async generatePaymentData(tradeRegResponse: KcpTradeRegResponse, request: PaymentInitiationRequest): Promise<{ paymentUrl: string; formData: KcpPaymentFormData }> {
    try {
      // Generate return URL that KCP will redirect to after payment
      const baseUrl = this.configService.get<string>('API_URL', 'http://localhost:3012');
      const returnUrl = `${baseUrl}/payment/kcp/return`;

      // Generate the action URL for the payment form (equivalent to /kcp_api_pay)
      const actionUrl = `${baseUrl}/payment/kcp/process`;

      // Based on KCP sample, we should return a payment page URL that contains form data
      // The frontend will use this to display a KCP payment form
      const frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3011');

      // Ensure amount is integer (no decimals for KRW)
      const amount = Math.floor(Math.abs(request.amount));

      // Generate payment form data that matches KCP's sample structure
      const paymentFormData = {
        site_cd: this.kcpConfig.getSiteCd(),
        site_name: 'HEC Payment',
        ordr_idxx: request.orderId,
        good_name: request.productName,
        good_mny: amount.toString(),
        buyr_name: request.buyerName,
        buyr_tel2: request.buyerPhone,
        buyr_mail: request.buyerEmail,
        pay_method: this.getPayMethodCode(request.paymentMethod),
        quotaopt: '12', // 최대 할부개월수
        // Required hidden fields that KCP will populate
        res_cd: '',
        res_msg: '',
        enc_info: '',
        enc_data: '',
        tran_cd: '',
        // Additional data for our backend
        return_url: returnUrl,
        action_url: actionUrl,
        tno: tradeRegResponse.tno,
        ordr_chk: tradeRegResponse.ordr_chk,
        kcp_sign_data: tradeRegResponse.kcp_sign_data
      };

      // Encode the form data as URL parameters
      const formParams = new URLSearchParams();
      Object.keys(paymentFormData).forEach(key => {
        formParams.append(key, paymentFormData[key]);
      });

      // Return a URL to our frontend payment page with the form data
      const paymentUrl = `${frontendUrl}/payment/kcp?${formParams.toString()}`;

      this.logger.log(`Generated KCP payment form URL: ${paymentUrl}`);
      return {
        paymentUrl,
        formData: paymentFormData
      };

    } catch (error) {
      this.logger.error(`Failed to generate payment URL: ${error.message}`, error.stack);
      throw new Error('Failed to generate payment URL');
    }
  }

  /**
   * Extract response code and message from KCP response
   * Handles different KCP response formats
   */
  private extractKcpResponseFields(kcpResponse: any): { code: string; message: string } {
    const code = kcpResponse.res_cd || kcpResponse.Code || '';
    const message = kcpResponse.res_msg || kcpResponse.Message || '';
    return { code, message };
  }

  /**
   * Parse KCP API response
   * KCP can return responses in different formats (JSON, form-encoded, or custom format)
   */
  private parseKcpResponse(responseData: any): any {
    try {
      // If response is already an object, return as-is
      if (typeof responseData === 'object' && responseData !== null) {
        return responseData;
      }

      // If response is a string, try to parse it
      if (typeof responseData === 'string') {
        // Try JSON first
        try {
          return JSON.parse(responseData);
        } catch {
          // If not JSON, try to parse as URL-encoded form data
          const params = new URLSearchParams(responseData);
          const result: any = {};
          for (const [key, value] of params.entries()) {
            result[key] = value;
          }
          return result;
        }
      }

      // Fallback: return the data as-is
      return responseData;
    } catch (error) {
      this.logger.error(`Failed to parse KCP response: ${error.message}`);
      throw new Error('Invalid KCP response format');
    }
  }

  /**
   * Retry mechanism for KCP API calls
   */
  private async retryKcpRequest<T>(
    requestFn: () => Promise<T>,
    maxRetries: number = this.kcpConfig.getRetryAttempts()
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error;
        this.logger.warn(`KCP API request attempt ${attempt} failed: ${error.message}`);

        // Don't retry on authentication or client errors
        if (error.response && error.response.status < 500) {
          throw error;
        }

        // Wait before retrying (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s, etc.
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }



  /**
   * Validate webhook signature
   */
  validateWebhookSignature(payload: string, signature: string): boolean {
    return this.kcpConfig.validateWebhookSignature(payload, signature);
  }
}
