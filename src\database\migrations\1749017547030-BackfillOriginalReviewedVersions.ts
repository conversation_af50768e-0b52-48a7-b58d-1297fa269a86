import { MigrationInterface, QueryRunner } from 'typeorm';

export class BackfillOriginalReviewedVersions1749017547030 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    console.log('Backfilling original reviewed versions for existing corrected entries...');

    // 1. Backfill regular diary entries that have corrections but no original reviewed version
    console.log('Processing regular diary entries...');

    // First, check if we have any entries that need backfilling
    const checkResult = await queryRunner.query(`
      SELECT COUNT(*) as count
      FROM diary_entry de
      INNER JOIN diary_correction dc ON de.id = dc.diary_entry_id
      WHERE de.original_reviewed_version_id IS NULL
    `);

    console.log(`Total diary entries with corrections but no original reviewed version: ${checkResult[0].count}`);

    if (parseInt(checkResult[0].count) === 0) {
      console.log('No diary entries need backfilling, skipping...');
    } else {
      const diaryEntriesWithCorrections = await queryRunner.query(`
        SELECT
          de.id,
          de.title,
          de.content,
          de.created_at,
          de.created_by,
          dc.created_at as correction_created_at
        FROM diary_entry de
        INNER JOIN diary_correction dc ON de.id = dc.diary_entry_id
        WHERE de.original_reviewed_version_id IS NULL
        ORDER BY dc.created_at
      `);

      console.log(`Processing ${diaryEntriesWithCorrections.length} diary entries...`);

      for (const entry of diaryEntriesWithCorrections) {
        try {
          // Calculate word count for the original content
          const wordCount = this.calculateWordCount(entry.content || '');

          console.log(`Processing diary entry ${entry.id} with ${wordCount} words`);

          // Create a history entry for the original version
          const historyResult = await queryRunner.query(`
            INSERT INTO diary_entry_history (
              diary_entry_id,
              title,
              content,
              version_number,
              is_latest,
              word_count,
              created_at,
              created_by,
              meta_data
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
          `, [
            entry.id,
            entry.title || '',
            entry.content || '',
            1, // version number
            false, // not latest since this is the original
            wordCount,
            entry.correction_created_at, // Use correction date as the "original" date
            entry.created_by,
            JSON.stringify({
              updateTrigger: 'backfill',
              significantChange: true,
              isOriginalReviewedVersion: true
            })
          ]);

          const historyId = historyResult[0].id;

          // Update the diary entry to reference this as the original reviewed version
          await queryRunner.query(`
            UPDATE diary_entry
            SET original_reviewed_version_id = $1,
                total_versions = COALESCE(total_versions, 0) + 1
            WHERE id = $2
          `, [historyId, entry.id]);

          console.log(`✅ Created original reviewed version ${historyId} for diary entry ${entry.id}`);
        } catch (error) {
          console.error(`❌ Error processing diary entry ${entry.id}:`, error.message);
          // Continue with other entries even if one fails
        }
      }
    }

    // 2. Backfill mission diary entries that have corrections but no original reviewed version
    console.log('Processing mission diary entries...');

    // Check mission entries that need backfilling
    const missionCheckResult = await queryRunner.query(`
      SELECT COUNT(*) as count
      FROM mission_diary_entry mde
      WHERE mde.status = 'reviewed'
        AND mde.original_reviewed_version_id IS NULL
        AND mde.reviewed_at IS NOT NULL
    `);

    console.log(`Total mission diary entries with reviews but no original reviewed version: ${missionCheckResult[0].count}`);

    if (parseInt(missionCheckResult[0].count) === 0) {
      console.log('No mission diary entries need backfilling, skipping...');
    } else {
      const missionEntriesWithCorrections = await queryRunner.query(`
        SELECT
          mde.id,
          mde.content,
          mde.created_at,
          mde.student_id as created_by,
          mde.reviewed_at as correction_created_at
        FROM mission_diary_entry mde
        WHERE mde.status = 'reviewed'
          AND mde.original_reviewed_version_id IS NULL
          AND mde.reviewed_at IS NOT NULL
        ORDER BY mde.reviewed_at
      `);

      console.log(`Processing ${missionEntriesWithCorrections.length} mission diary entries...`);

      for (const entry of missionEntriesWithCorrections) {
        try {
          // Calculate word count for the original content
          const wordCount = this.calculateWordCount(entry.content || '');

          console.log(`Processing mission entry ${entry.id} with ${wordCount} words`);

          // Create a history entry for the original version
          const historyResult = await queryRunner.query(`
            INSERT INTO mission_diary_entry_history (
              mission_entry_id,
              content,
              version_number,
              is_latest,
              word_count,
              created_at,
              created_by,
              meta_data
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            RETURNING id
          `, [
            entry.id,
            entry.content || '',
            1, // version number
            false, // not latest since this is the original
            wordCount,
            entry.correction_created_at, // Use review date as the "original" date
            entry.created_by,
            JSON.stringify({
              updateTrigger: 'backfill',
              significantChange: true,
              isOriginalReviewedVersion: true
            })
          ]);

          const historyId = historyResult[0].id;

          // Update the mission diary entry to reference this as the original reviewed version
          await queryRunner.query(`
            UPDATE mission_diary_entry
            SET original_reviewed_version_id = $1,
                total_versions = COALESCE(total_versions, 0) + 1
            WHERE id = $2
          `, [historyId, entry.id]);

          console.log(`✅ Created original reviewed version ${historyId} for mission entry ${entry.id}`);
        } catch (error) {
          console.error(`❌ Error processing mission entry ${entry.id}:`, error.message);
          // Continue with other entries even if one fails
        }
      }
    }

    console.log('Successfully backfilled original reviewed versions');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    console.log('Removing backfilled original reviewed versions...');

    try {
      // First, reset the original_reviewed_version_id references
      console.log('Resetting diary entry references...');
      const diaryUpdateResult = await queryRunner.query(`
        UPDATE diary_entry
        SET original_reviewed_version_id = NULL,
            total_versions = GREATEST(total_versions - 1, 0)
        WHERE original_reviewed_version_id IN (
          SELECT id FROM diary_entry_history
          WHERE meta_data::text LIKE '%"isOriginalReviewedVersion":true%'
        )
      `);
      console.log(`Reset ${diaryUpdateResult.affectedRows || 0} diary entries`);

      console.log('Resetting mission diary entry references...');
      const missionUpdateResult = await queryRunner.query(`
        UPDATE mission_diary_entry
        SET original_reviewed_version_id = NULL,
            total_versions = GREATEST(total_versions - 1, 0)
        WHERE original_reviewed_version_id IN (
          SELECT id FROM mission_diary_entry_history
          WHERE meta_data::text LIKE '%"isOriginalReviewedVersion":true%'
        )
      `);
      console.log(`Reset ${missionUpdateResult.affectedRows || 0} mission diary entries`);

      // Then remove the backfilled history entries
      console.log('Removing backfilled diary history entries...');
      const diaryHistoryDeleteResult = await queryRunner.query(`
        DELETE FROM diary_entry_history
        WHERE meta_data::text LIKE '%"isOriginalReviewedVersion":true%'
      `);
      console.log(`Removed ${diaryHistoryDeleteResult.affectedRows || 0} diary history entries`);

      console.log('Removing backfilled mission diary history entries...');
      const missionHistoryDeleteResult = await queryRunner.query(`
        DELETE FROM mission_diary_entry_history
        WHERE meta_data::text LIKE '%"isOriginalReviewedVersion":true%'
      `);
      console.log(`Removed ${missionHistoryDeleteResult.affectedRows || 0} mission diary history entries`);

      console.log('Successfully removed backfilled original reviewed versions');
    } catch (error) {
      console.error('Error during rollback:', error.message);
      throw error;
    }
  }

  /**
   * Calculate word count for content
   */
  private calculateWordCount(content: string): number {
    if (!content || content.trim().length === 0) {
      return 0;
    }
    return content.trim().split(/\s+/).length;
  }
}
