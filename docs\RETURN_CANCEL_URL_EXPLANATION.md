# Return URL and Cancel URL Explanation

## Overview

The `returnUrl` and `cancelUrl` in the checkout request body are **frontend URLs** that determine where users are redirected after completing or cancelling their payment.

## Current Checkout Request Format

```json
{
  "paymentMethod": "kcp_card",
  "useRewardPoints": false,
  "returnUrl": "https://example.com/payment/success",
  "cancelUrl": "https://example.com/payment/cancel"
}
```

## URL Usage in Payment Flow

### **Complete Payment Flow with URLs**

```
1. User clicks checkout
   ↓
2. POST /shop/cart/checkout
   Body: { returnUrl: "http://frontend.com/payment/success", cancelUrl: "http://frontend.com/payment/cancel" }
   ↓
3. Backend creates KCP trade registration
   ↓
4. Backend returns payment URL: "http://frontend.com/payment/kcp?tno=123&..."
   ↓
5. Frontend redirects user to payment URL
   ↓
6. User completes payment on KCP page
   ↓
7. KC<PERSON> calls m_Completepayment() callback
   ↓
8. Form submits to: POST /payment/kcp/process
   ↓
9. Backend processes payment and completes checkout
   ↓
10. Backend redirects to SUCCESS or FAILURE page based on result
```

## URL Types and Their Purpose

### **1. Return URL (Success)**
- **Purpose**: Where to redirect users after **successful** payment
- **When Used**: After payment is completed and verified
- **Example**: `http://**************:3010/payment/success`

### **2. Cancel URL (Cancellation)**  
- **Purpose**: Where to redirect users when they **cancel** payment
- **When Used**: When user closes payment window or clicks cancel
- **Example**: `http://**************:3010/payment/cancel`

### **3. Payment URL (Generated by Backend)**
- **Purpose**: The KCP payment page URL with form data
- **When Used**: Immediately after checkout to start payment
- **Example**: `http://**************:3001/payment/kcp?tno=123&...`

## Real Application URLs

Based on your application setup:

### **Frontend URLs (User-facing pages)**
```json
{
  "returnUrl": "http://**************:3010/payment/success",
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

### **Backend URLs (API endpoints)**
```
Payment Processing: http://**************:3012/payment/kcp/process
Payment Return: http://**************:3012/payment/kcp/return
```

### **Payment Page URL (Generated)**
```
KCP Payment Page: http://**************:3001/payment/kcp?site_cd=T0000&tno=123&...
```

## URL Flow in Your Application

### **Success Flow**
```
1. User completes payment successfully
   ↓
2. KCP calls m_Completepayment() with res_cd='0000'
   ↓
3. Form submits to: POST /payment/kcp/process
   ↓
4. Backend verifies payment and completes checkout
   ↓
5. Backend redirects to: http://**************:3010/payment/success?orderId=ORDER-123
```

### **Failure Flow**
```
1. Payment fails or user cancels
   ↓
2. KCP calls m_Completepayment() with res_cd!='0000'
   ↓
3. JavaScript redirects to: http://**************:3010/payment/cancel?error=Payment+failed
```

### **Cancel Flow**
```
1. User closes payment window
   ↓
2. Frontend detects window close
   ↓
3. Frontend redirects to: http://**************:3010/payment/cancel?reason=user_cancelled
```

## Frontend Pages to Implement

### **1. Success Page** (`/payment/success`)
```typescript
// http://**************:3010/payment/success?orderId=ORDER-123&transactionId=TXN-456

const SuccessPage = () => {
  const orderId = useSearchParams().get('orderId');
  const transactionId = useSearchParams().get('transactionId');
  
  return (
    <div>
      <h1>Payment Successful!</h1>
      <p>Order ID: {orderId}</p>
      <p>Transaction ID: {transactionId}</p>
      <button onClick={() => router.push('/orders')}>View Orders</button>
    </div>
  );
};
```

### **2. Cancel/Failure Page** (`/payment/cancel`)
```typescript
// http://**************:3010/payment/cancel?error=Payment+failed&reason=insufficient_funds

const CancelPage = () => {
  const error = useSearchParams().get('error');
  const reason = useSearchParams().get('reason');
  
  return (
    <div>
      <h1>Payment Cancelled</h1>
      <p>Error: {error}</p>
      <p>Reason: {reason}</p>
      <button onClick={() => router.push('/cart')}>Return to Cart</button>
    </div>
  );
};
```

### **3. KCP Payment Page** (`/payment/kcp`)
```typescript
// http://**************:3001/payment/kcp?site_cd=T0000&tno=123&...

const KcpPaymentPage = () => {
  // Parse URL parameters
  // Display KCP payment form
  // Handle m_Completepayment callback
  // Submit form to /payment/kcp/process
};
```

## URL Configuration by Environment

### **Development**
```json
{
  "returnUrl": "http://localhost:3010/payment/success",
  "cancelUrl": "http://localhost:3010/payment/cancel"
}
```

### **Staging**
```json
{
  "returnUrl": "http://**************:3010/payment/success", 
  "cancelUrl": "http://**************:3010/payment/cancel"
}
```

### **Production**
```json
{
  "returnUrl": "https://your-domain.com/payment/success",
  "cancelUrl": "https://your-domain.com/payment/cancel"
}
```

## Important Notes

### **1. URL Validation**
The backend should validate that return/cancel URLs are from allowed domains to prevent redirect attacks.

### **2. URL Parameters**
Success/failure pages should handle URL parameters for order details and error information.

### **3. User Experience**
- Success page should show order confirmation and next steps
- Cancel page should allow users to retry payment or return to cart
- Payment page should handle loading states and errors

### **4. Security**
- Use HTTPS in production
- Validate redirect URLs
- Include CSRF protection
- Handle sensitive data appropriately

## Summary

- **returnUrl**: Frontend success page (`/payment/success`)
- **cancelUrl**: Frontend cancel/failure page (`/payment/cancel`)  
- **paymentUrl**: Generated KCP payment page (`/payment/kcp`)
- **processUrl**: Backend processing endpoint (`/payment/kcp/process`)

The URLs create a complete user journey from checkout → payment → completion → confirmation.
