import { Controller, Get, Post, Body, Param, Delete, UseGuards, Req, Patch, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { ShoppingCartService } from './shopping-cart.service';
import { AddToCartDto, UpdateCartItemDto, ShoppingCartResponseDto, CheckoutDto, CheckoutResponseDto } from '../../database/models/shopping-cart.dto';
import { ApiResponse } from '../../common/dto/api-response.dto';
import { ApiOkResponseWithType, ApiErrorResponse } from '../../common/decorators/api-response.decorator';
import { JwtAuthGuard } from '../../common/guards/jwt.guard';
import { StudentGuard } from '../../common/guards/student.guard';
import { StrictStudentOnly } from '../../common/decorators/strict-student-only.decorator';
import { Request } from 'express';

// Define a custom interface to extend the Express Request type
interface RequestWithUser extends Request {
  user: { id: string; [key: string]: any };
}

@ApiTags('Shopping Cart')
@Controller('shop/cart')
@UseGuards(JwtAuthGuard, StudentGuard)
@StrictStudentOnly()
@ApiBearerAuth('JWT-auth')
export class ShoppingCartController {
  constructor(private readonly shoppingCartService: ShoppingCartService) {}

  @Get()
  @ApiOperation({
    summary: 'Get shopping cart',
    description: 'Get the current user\'s shopping cart'
  })
  @ApiOkResponseWithType(ShoppingCartResponseDto, 'Shopping cart retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getCart(@Req() req: RequestWithUser): Promise<ApiResponse<ShoppingCartResponseDto>> {
    const cart = await this.shoppingCartService.getCartResponse(req.user['id']);
    return ApiResponse.success(cart, 'Shopping cart retrieved successfully');
  }

  @Post('add')
  @ApiOperation({
    summary: 'Add item to cart',
    description: 'Add an item to the shopping cart'
  })
  @ApiBody({
    type: AddToCartDto,
    description: 'Item to add to cart'
  })
  @ApiOkResponseWithType(ShoppingCartResponseDto, 'Item added to cart successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Shop item not found')
  async addToCart(
    @Req() req: RequestWithUser,
    @Body() addToCartDto: AddToCartDto
  ): Promise<ApiResponse<ShoppingCartResponseDto>> {
    const cart = await this.shoppingCartService.addToCart(req.user['id'], addToCartDto);
    return ApiResponse.success(cart, 'Item added to cart successfully');
  }

  @Patch('items/:id')
  @ApiOperation({
    summary: 'Update cart item',
    description: 'Update the quantity of an item in the cart'
  })
  @ApiParam({
    name: 'id',
    description: 'Cart item ID',
    type: String
  })
  @ApiBody({
    type: UpdateCartItemDto,
    description: 'Updated item details'
  })
  @ApiOkResponseWithType(ShoppingCartResponseDto, 'Cart item updated successfully')
  @ApiErrorResponse(400, 'Invalid input')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Cart item not found')
  async updateCartItem(
    @Req() req: RequestWithUser,
    @Param('id') id: string,
    @Body() updateCartItemDto: UpdateCartItemDto
  ): Promise<ApiResponse<ShoppingCartResponseDto>> {
    const cart = await this.shoppingCartService.updateCartItem(req.user['id'], id, updateCartItemDto);
    return ApiResponse.success(cart, 'Cart item updated successfully');
  }

  @Delete('items/:id')
  @ApiOperation({
    summary: 'Remove cart item',
    description: 'Remove an item from the cart'
  })
  @ApiParam({
    name: 'id',
    description: 'Cart item ID',
    type: String
  })
  @ApiOkResponseWithType(ShoppingCartResponseDto, 'Cart item removed successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Cart item not found')
  async removeCartItem(
    @Req() req: RequestWithUser,
    @Param('id') id: string
  ): Promise<ApiResponse<ShoppingCartResponseDto>> {
    const cart = await this.shoppingCartService.removeCartItem(req.user['id'], id);
    return ApiResponse.success(cart, 'Cart item removed successfully');
  }

  @Delete('clear')
  @ApiOperation({
    summary: 'Clear cart',
    description: 'Remove all items from the cart'
  })
  @ApiOkResponseWithType(ShoppingCartResponseDto, 'Cart cleared successfully')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async clearCart(@Req() req: RequestWithUser): Promise<ApiResponse<ShoppingCartResponseDto>> {
    const cart = await this.shoppingCartService.clearCart(req.user['id']);
    return ApiResponse.success(cart, 'Cart cleared successfully');
  }

  @Post('checkout')
  @ApiOperation({
    summary: 'Checkout',
    description: 'Process checkout for the items in the cart'
  })
  @ApiBody({
    type: CheckoutDto,
    description: 'Checkout details'
  })
  @ApiOkResponseWithType(CheckoutResponseDto, 'Checkout completed successfully')
  @ApiErrorResponse(400, 'Invalid input or empty cart')
  @ApiErrorResponse(401, 'Unauthorized')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async checkout(
    @Req() req: RequestWithUser,
    @Body() checkoutDto: CheckoutDto
  ): Promise<ApiResponse<CheckoutResponseDto>> {
    const result = await this.shoppingCartService.checkout(req.user['id'], checkoutDto);
    // Check if this is a KCP payment (which returns payment initiation, not completion)
    const message = result.paymentUrl ? 'Payment initiated successfully' : 'Checkout completed successfully';
    return ApiResponse.success(result, message);
  }
}
