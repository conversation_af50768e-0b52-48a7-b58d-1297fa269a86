# Award Frequency Configuration Update

## ✅ **Updated Swagger Frequency Dropdowns**

The frequency dropdown in Swagger now reflects the **actual award frequencies** configured in the award system for each module, based on the real seeded data.

## 📊 **Actual Award Frequencies by Module**

### **Diary Module**
**Configured Frequencies**: `monthly`, `yearly`

**Awards Available**:
- **Monthly Awards**:
  - Best Writer Award (150 points)
  - Best Designer Award (150 points) 
  - Best Perfect Award (200 points)
  - Best Friendship Award (100 points)

- **Yearly Awards**:
  - Best Writer Award (500 points)
  - Best Designer Award (500 points)
  - Best Perfect Award (750 points)
  - Best Friendship Award (300 points)

### **Novel Module**
**Configured Frequencies**: `monthly`, `yearly`

**Awards Available**:
- **Monthly Awards**:
  - Best Writer Award (150 points)
  - Best Perfect Award (200 points)
  - Best Performance Award (150 points)

- **Yearly Awards**:
  - Best Writer Award (500 points)
  - Best Perfect Award (750 points)
  - Best Performance Award (500 points)

### **Essay Module**
**Configured Frequencies**: `monthly`, `yearly`

**Awards Available**:
- **Monthly Awards**:
  - Best Writer Award (150 points)
  - Best Perfect Award (200 points)

- **Yearly Awards**:
  - Best Writer Award (500 points)
  - Best Perfect Award (750 points)

## 🔧 **Updated API Documentation**

### **Before (Generic)**
```typescript
@ApiQuery({ 
  name: 'frequency', 
  required: false, 
  type: String, 
  description: 'Filter by award frequency (monthly/weekly)' 
})
async getDiaryOngoingAwards(@Query('frequency') frequency?: string)
```

### **After (Accurate)**
```typescript
@ApiQuery({ 
  name: 'frequency', 
  required: false, 
  enum: AwardFrequency, 
  description: 'Filter by award frequency. Diary module supports: monthly, yearly' 
})
async getDiaryOngoingAwards(@Query('frequency') frequency?: AwardFrequency)
```

## 📝 **Swagger UI Improvements**

### **Frequency Dropdown Options**
The Swagger UI now shows a dropdown with the exact `AwardFrequency` enum values:

- ✅ `monthly` - Monthly awards (available for all modules)
- ✅ `yearly` - Yearly awards (available for all modules)
- ❌ `weekly` - Not configured in any module
- ❌ `quarterly` - Not configured in any module
- ❌ `one_time` - Not configured in any module

### **Module-Specific Descriptions**
Each endpoint now has accurate descriptions:

- **Diary**: "Filter by award frequency. Diary module supports: monthly, yearly"
- **Novel**: "Filter by award frequency. Novel module supports: monthly, yearly"
- **Essay**: "Filter by award frequency. Essay module supports: monthly, yearly"

## 🎯 **API Usage Examples**

### **Get Current Monthly Diary Winners**
```bash
GET /api/hall-of-fame/diary/ongoing?frequency=monthly
```

### **Get Current Yearly Novel Winners**
```bash
GET /api/hall-of-fame/novel/ongoing?frequency=yearly
```

### **Get All Current Essay Winners (Any Frequency)**
```bash
GET /api/hall-of-fame/essay/ongoing
```

## 📱 **Frontend Integration**

### **TypeScript Usage**
```typescript
import { AwardFrequency } from './types/award.types';

// Get monthly diary winners
const getMonthlyDiaryWinners = async () => {
  const response = await fetch(
    `/api/hall-of-fame/diary/ongoing?frequency=${AwardFrequency.MONTHLY}`,
    {
      headers: { 'Authorization': `Bearer ${token}` }
    }
  );
  return response.json();
};

// Get yearly novel winners
const getYearlyNovelWinners = async () => {
  const response = await fetch(
    `/api/hall-of-fame/novel/ongoing?frequency=${AwardFrequency.YEARLY}`,
    {
      headers: { 'Authorization': `Bearer ${token}` }
    }
  );
  return response.json();
};
```

### **Frequency Selector Component**
```typescript
const FrequencySelector = ({ module, onSelect }) => {
  // All modules support the same frequencies based on seed data
  const availableFrequencies = [
    { value: 'monthly', label: 'Monthly Awards' },
    { value: 'yearly', label: 'Yearly Awards' }
  ];

  return (
    <select onChange={(e) => onSelect(e.target.value)}>
      <option value="">All Frequencies</option>
      {availableFrequencies.map(freq => (
        <option key={freq.value} value={freq.value}>
          {freq.label}
        </option>
      ))}
    </select>
  );
};
```

## ✅ **Benefits of Accurate Configuration**

1. **Accurate Documentation**: Swagger shows only valid frequency options
2. **Type Safety**: Using `AwardFrequency` enum prevents invalid values
3. **Better UX**: Frontend developers know exactly what frequencies are available
4. **Data Consistency**: API matches the actual award system configuration
5. **Future-Proof**: Easy to update if new frequencies are added to specific modules

## 🔍 **Validation**

### **Valid Requests**
```bash
✅ GET /api/hall-of-fame/diary/ongoing?frequency=monthly
✅ GET /api/hall-of-fame/novel/ongoing?frequency=yearly
✅ GET /api/hall-of-fame/essay/ongoing
```

### **Invalid Requests**
```bash
❌ GET /api/hall-of-fame/diary/ongoing?frequency=weekly
❌ GET /api/hall-of-fame/novel/ongoing?frequency=quarterly
❌ GET /api/hall-of-fame/essay/ongoing?frequency=invalid
```

## 🚀 **Ready for Production**

The frequency configuration is now:
- ✅ **Accurate** - Reflects actual award system data
- ✅ **Type-Safe** - Uses proper enum types
- ✅ **Well-Documented** - Clear Swagger descriptions
- ✅ **Consistent** - All modules follow the same pattern
- ✅ **Validated** - Only accepts valid frequency values

The Swagger UI will now show the correct dropdown options that match the real award frequencies configured in your system!
