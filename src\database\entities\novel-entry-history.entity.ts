import { <PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON>ne, Jo<PERSON><PERSON><PERSON>umn, Index } from 'typeorm';
import { AuditableBaseEntity } from './base-entity';
import { NovelEntry } from './novel-entry.entity';

@Entity()
@Index(['novelEntryId', 'versionNumber'])
@Index(['novelEntryId', 'isLatest'])
@Index(['createdAt'])
export class NovelEntryHistory extends AuditableBaseEntity {
  @Column({ name: 'novel_entry_id', type: 'uuid' })
  novelEntryId: string;

  @ManyToOne(() => NovelEntry, entry => entry.versions)
  @JoinColumn({ name: 'novel_entry_id' })
  novelEntry: NovelEntry;

  @Column({ name: 'content', type: 'text' })
  content: string;

  @Column({ name: 'version_number', type: 'integer' })
  versionNumber: number;

  @Column({ name: 'is_latest', type: 'boolean', default: false })
  isLatest: boolean;

  @Column({ name: 'word_count', type: 'integer' })
  wordCount: number;

  @Column({
    name: 'meta_data',
    type: 'json',
    nullable: true
  })
  metaData: any;
}
