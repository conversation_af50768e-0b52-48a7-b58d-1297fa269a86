import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Request,
  Parse<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Param
} from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiParam, ApiQuery, ApiTags } from "@nestjs/swagger";
import { JwtAuthGuard } from "src/common/guards/jwt.guard";
import { StudentGuard } from "src/common/guards/student.guard";
import { ApiResponse } from "src/common/dto/api-response.dto";
import { ApiOkResponseWithType, ApiOkResponseWithPagedListType, ApiErrorResponse } from 'src/common/decorators/api-response.decorator';
import { PagedListDto } from "src/common/models/paged-list.dto";
import { QAMissionService } from "./qa-mission.service";
import { QASubmissionService } from "./student-qa-mission.service";
import {
  QAMissionResponseDto,
  QAMissionPaginationDto,
  StartQATaskDto,
  CreateQATaskSubmissionDto,
  QATaskSubmissionUpdate,
  QATaskSubmissionDto,
  QATaskDto
} from "src/database/models/qa-mission.dto";

@ApiTags('Student Q&A Mission')
@ApiBearerAuth('JWT-auth')
@Controller('student-qa-mission')
export class StudentQAMissionController {
  constructor(
    private readonly qaMissionService: QAMissionService,
    private readonly qaSubmissionService: QASubmissionService
  ){}

  // @Get('getMission')
  // @UseGuards(JwtAuthGuard, StudentGuard)
  // @ApiOperation({ summary: 'Get all QA missions' })
  // @ApiQuery({
  //   name: 'page',
  //   required: false,
  //   type: Number,
  //   description: 'Page number',
  // })
  // @ApiQuery({
  //   name: 'limit',
  //   required: false,
  //   type: Number,
  //   description: 'Number of items per page',
  // })
  // @ApiQuery({
  //   name: 'sortBy',
  //   required: false,
  //   type: String,
  //   description: 'Field to sort by',
  // })
  // @ApiQuery({
  //   name: 'sortDirection',
  //   required: false,
  //   type: String,
  //   enum: ['ASC', 'DESC'],
  //   description: 'Sort order (asc or desc)',
  // })
  // @ApiOkResponseWithPagedListType(QAMissionResponseDto, 'Retrieved all QA missions')
  // async findAll(@Query() paginationDto?: QAMissionPaginationDto): Promise<ApiResponse<PagedListDto<QAMissionResponseDto>>> {
  //   const result = await this.qaMissionService.findAllForStudents(paginationDto);
  //   return ApiResponse.success(result, 'Retrieved all QA missions');
  // }

  @Get('getMissionList')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Get all QA missions' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortDirection',
    required: false,
    type: String,
    enum: ['ASC', 'DESC'],
    description: 'Sort order (asc or desc)',
  })
  @ApiOkResponseWithPagedListType(QAMissionResponseDto, 'Retrieved all QA missions')
  async findList(
    @Request() req,
    @Query() paginationDto?: QAMissionPaginationDto): Promise<ApiResponse<PagedListDto<QAMissionResponseDto>>> {
    const result = await this.qaMissionService.findListForStudents(req.user.id, paginationDto);
    return ApiResponse.success(result, 'Retrieved all QA missions');
  }

  @Post('start/task')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Start a QA task' })
  @ApiBody({
    type: StartQATaskDto,
    description: 'QA task start data',
    examples: {
      example1: {
        value: {
          taskId: '123e4567-e89b-12d3-a456-426614174000',
        }
      }
    }
  })
  @ApiOkResponseWithType(QATaskSubmissionDto, 'Task started successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task not found')
  @ApiErrorResponse(409, 'Task already started')
  @ApiErrorResponse(500, 'Internal server error')
  async startTask(
    @Body() startQATask: StartQATaskDto,
    @Request() req
  ): Promise<ApiResponse<QATaskSubmissionDto>> {
    const result = await this.qaSubmissionService.startTask(startQATask.taskId, req.user.id);
    return ApiResponse.success(result, 'Task started successfully');
  }

  @Post('submit/task')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Submit a QA task' })
  @ApiBody({
    type: CreateQATaskSubmissionDto,
    description: 'QA task submission data',
    examples: {
      example1: {
        value: {
          taskId: '123e4567-e89b-12d3-a456-426614174000',
          content: 'This is my QA submission',
          wordCount: 75,
          metaData: {
            timeSpent: 120,
          }
        }
      }
    }
  })
  @ApiOkResponseWithType(QATaskSubmissionDto, 'QA task submitted successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async submitTask(
    @Body() createQATaskSubmissionDto: CreateQATaskSubmissionDto,
    @Request() req
  ): Promise<ApiResponse<QATaskSubmissionDto>> {
    const result = await this.qaSubmissionService.submitQA(createQATaskSubmissionDto, req.user.id);
    return ApiResponse.success(result, 'Task submitted successfully');
  }

  @Post('submit/task/update')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Update a QA task submission' })
  @ApiBody({
    type: QATaskSubmissionUpdate,
    description: 'QA task submission update data',
    examples: {
      example1: {
        value: {
          submissionId: '123e4567-e89b-12d3-a456-426614174000',
          content: 'This is my updated QA submission',
          wordCount: 75
        }
      }
    }
  })
  @ApiOkResponseWithType(QATaskSubmissionDto, 'QA task submission updated successfully')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  @ApiErrorResponse(404, 'Task submission not found')
  async updateTaskSubmission(
    @Body() updateTaskSubmissionDto: QATaskSubmissionUpdate,
    @Request() req
  ): Promise<ApiResponse<QATaskSubmissionDto>> {
    const result = await this.qaSubmissionService.autoSaveContent(updateTaskSubmissionDto, req.user.id);
    return ApiResponse.success(result, 'Task submission updated successfully');
  }

  @Get('activeTask')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Get active QA task for the logged-in user' })
  @ApiOkResponseWithType(QATaskSubmissionDto, 'Retrieved active QA task')
  @ApiErrorResponse(400, 'Invalid input data')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Student access required')
  async getActiveTask(
    @Request() req
  ): Promise<ApiResponse<QATaskSubmissionDto>> {
    const result = await this.qaSubmissionService.getActiveTask(req.user.id);
    return ApiResponse.success(result, 'Retrieved active QA task');
  }

  @Get('task/:id')
  @UseGuards(JwtAuthGuard, StudentGuard)
  @ApiOperation({ summary: 'Get a specific QA task by ID' })
  @ApiParam({ name: 'id', description: 'ID of the QA task to retrieve', type: String })
  @ApiOkResponseWithType(QATaskDto, 'QA task retrieved successfully')
  @ApiErrorResponse(401, 'Unauthorized - Authentication required')
  @ApiErrorResponse(403, 'Forbidden - Admin access required')
  @ApiErrorResponse(404, 'QA task not found')
  async findTask(
    @Param('id', ParseUUIDPipe) id: string,
    @Request() req
  ): Promise<ApiResponse<any>> {
    const result = await this.qaSubmissionService.findStudentTaskById(id, req.user.id);
    return ApiResponse.success(result, 'QA task retrieved successfully');
  }
}